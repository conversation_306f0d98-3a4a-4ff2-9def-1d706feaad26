
import numpy as np
import matplotlib.pyplot as plt
import xarray as xr
from pyproj import Geod


# 加载 NetCDF 数据
ds = xr.open_dataset("D:/ocean_data/cmems_mod_glo_phy_my_0.083deg_P1D-m_1751179968305.nc")
uo = ds['uo'].isel(time=0).values
vo = ds['vo'].isel(time=0).values
lat = ds['latitude'].values
lon = ds['longitude'].values
uo_plot = uo[0]
vo_plot = vo[0]
lon_grid, lat_grid = np.meshgrid(lon, lat)

# 定义滑翔机圆形结构函数
def init_gliders_circle(num_gliders, radius_km, lat0, lon0):
    R_earth = 6371.0
    radius_deg = (radius_km / R_earth) * (180 / np.pi)
    states = []
    angles = np.linspace(0, 2 * np.pi, num_gliders, endpoint=False)
    for angle in angles:
        dlat = radius_deg * np.sin(angle)
        dlon = radius_deg * np.cos(angle) / np.cos(np.radians(lat0))
        lat_pos = lat0 + dlat
        lon_pos = lon0 + dlon
        psi = angle + np.pi
        state = np.array([0, 0, 0, 0, 0, lon_pos, lat_pos, 0, 0, psi])
        states.append(state)
    return np.array(states)

# 经纬度转为相对坐标
geod = Geod(ellps='WGS84')
center_lat = (lat[0] + lat[-1]) / 2
center_lon = (lon[0] + lon[-1]) / 2
gliders_init = init_gliders_circle(10, 5, center_lat, center_lon)

dx_km = np.zeros_like(lon_grid)
dy_km = np.zeros_like(lat_grid)
for i in range(lat_grid.shape[0]):
    for j in range(lat_grid.shape[1]):
        az12, _, dist = geod.inv(center_lon, center_lat, lon_grid[i, j], lat_grid[i, j])
        dx_km[i, j] = dist * np.sin(np.radians(az12)) / 1000
        dy_km[i, j] = dist * np.cos(np.radians(az12)) / 1000

glider_x_km, glider_y_km = [], []
for g in gliders_init:
    az12, _, dist = geod.inv(center_lon, center_lat, g[5], g[6])
    dx = dist * np.sin(np.radians(az12)) / 1000
    dy = dist * np.cos(np.radians(az12)) / 1000
    glider_x_km.append(dx)
    glider_y_km.append(dy)

# 平移坐标原点至图左下角
x_shift, y_shift = dx_km.min(), dy_km.min()
dx_km -= x_shift
dy_km -= y_shift
glider_x_km = np.array(glider_x_km) - x_shift
glider_y_km = np.array(glider_y_km) - y_shift
print("X范围: 0 ~", dx_km.max(), "km")
print("Y范围: 0 ~", dy_km.max(), "km")

# 精确控制图形和坐标原点对齐
fig, ax = plt.subplots(figsize=(8, 8))
speed = np.clip(np.sqrt(uo_plot**2 + vo_plot**2), 0.06, 0.11)

contour = ax.contourf(
    dx_km, dy_km, speed,
    cmap='RdBu_r',     # 颜色图选择，比如 'seismic'、'bwr'、'Blues'
    levels=50,          # 等高线数
    vmin=0.06,          # 手动设置最小值
    vmax=0.11       # 手动设置最大值
)

tick_min = 0.06
tick_max = 0.11
tick_num = 11  # 11个刻度
cbar = fig.colorbar(contour, ax=ax, ticks=np.linspace(tick_min, tick_max, tick_num))
cbar.set_label('Current Speed [m/s]', fontsize=16)
cbar.ax.tick_params(labelsize=14)

cbar.set_label('Current Speed [m/s]', fontsize=16)      # 设置色条标签字体大小
cbar.ax.tick_params(labelsize=14)                       # 设置色条刻度字体大小




from scipy.ndimage import zoom

# 放大倍数（越大越密）
factor = 2
uo_dense = zoom(uo_plot, factor)
vo_dense = zoom(vo_plot, factor)
x_dense = zoom(dx_km, factor)
y_dense = zoom(dy_km, factor)

# 画更密的箭头
ax.quiver(x_dense, y_dense, uo_dense, vo_dense, scale=3e0, color='k', width=0.002)


# 绘制滑翔机蓝点
# 将首尾点闭合以连成圆
glider_x_km_closed = np.append(glider_x_km, glider_x_km[0])
glider_y_km_closed = np.append(glider_y_km, glider_y_km[0])

# 绘制滑翔机连线 & 三角形点
ax.plot(glider_x_km_closed, glider_y_km_closed, color='red', linewidth=1.5, linestyle='-')  # 连线
ax.plot(glider_x_km, glider_y_km, marker='^', color='red', markersize=8, linestyle='None')  # 三角形



# 精确设置坐标轴原点
ax.spines['left'].set_position(('data', 0))
ax.spines['bottom'].set_position(('data', 0))
ax.spines['right'].set_color('none')
ax.spines['top'].set_color('none')

# 设置刻度位置
ax.xaxis.set_ticks_position('bottom')
ax.yaxis.set_ticks_position('left')
ax.set_xticks(np.arange(0, dx_km.max(), 20))
ax.set_yticks(np.arange(0, dy_km.max(), 20))

# 标签 & 样式
ax.set_xlabel("X [km]",fontsize=15)
ax.set_ylabel("Y [km]",fontsize=15)
ax.set_title("Glider Initial Positions ",fontsize=20)
ax.set_aspect('equal')
ax.grid(False)

# 紧凑布局，去除白边
fig.subplots_adjust(left=0.12, right=0.92, top=0.92, bottom=0.12)

# 打印横纵坐标范围
print("X范围: 0 ~", dx_km.max(), "km")
print("Y范围: 0 ~", dy_km.max(), "km")

plt.show()

