import numpy as np

def LimitMaxMin(val, vmax, vmin):
    return np.clip(val, vmin, vmax)

def Infante_3d_with_current(t, x, formom, current):
    """
    Infante AUV 模型 + 海流扰动
    状态向量 x = [u, v, w, q, r, x_p, y_p, z_p, theta, psi]^T
    控制输入 formom = [Fu, deltav, deltah]^T
    current: 三维海流速度 [u_c, v_c, w_c]
    """
    dydt = np.zeros(10)

    # 控制输入
    Fu, deltav, deltah = formom

    # 状态变量
    u, v, w      = x[0], x[1], x[2]
    q, r         = x[3], x[4]
    xx, yy, zz   = x[5], x[6], x[7]
    theta, psi   = x[8], x[9]

    # 模型参数
    m   = 185.0
    g   = 9.85
    B   = m * g
    W   = B * 0.99

    zg, zb = 0.01, -0.01
    Xu, Xuu = 70.0, 100.0
    Yv, Yvv = 100.0, 200.0
    Zw, Zww = 100.0, 200.0
    Mq, Mqq = 50.0, 100.0
    Nr, Nrr = 50.0, 100.0
    m1, m2, m3 = 215.0, 265.0, 265.0
    m4, m5 = 80.0, 80.0

    # 重力浮力
    g1 = (W - B) * np.cos(theta)
    g2 = (zg * W - zb * B) * np.sin(theta)

    # 阻尼
    d1 = Xu + Xuu * abs(u)
    d2 = Yv + Yvv * abs(v)
    d3 = Zw + Zww * abs(w)
    d4 = Mq + Mqq * abs(q)
    d5 = Nr + Nrr * abs(r)

    # 动力学
    dydt[0] = (1 / m1) * (m2 * v * r - m3 * w * q - d1 * u + Fu)
    dydt[1] = -(1 / m2) * (m1 * u * r + d2 * v)
    dydt[2] = (1 / m3) * (m1 * u * q - d3 * w + g1)
    dydt[3] = (1 / m4) * ((m1 - m3) * u * w - d4 * q - g2 + deltah)
    dydt[4] = (1 / m5) * ((m1 - m2) * u * v - d5 * r + deltav)

    # 饱和
    dydt[0] = LimitMaxMin(dydt[0], 0.25, -0.05)  # 原值为0.5/-0.1
    dydt[1] = LimitMaxMin(dydt[1], 0.25, -0.25)  # 原值为0.5/-0.5
    dydt[2] = LimitMaxMin(dydt[2], 0.25, -0.25)  # 原值为0.5/-0.5
    dydt[3] = LimitMaxMin(dydt[3], 0.1, -0.1)
    dydt[4] = LimitMaxMin(dydt[4], 0.1, -0.1)

    # === 加入海流扰动 ===
    u_total = u + current[0]
    v_total = v + current[1]
    w_total = w + current[2]
    
    # 海流对滑翔机速度的直接影响（添加耦合项）
    # 添加海流梯度扰动，使海流能够影响滑翔机的速度状态
    current_coupling = 0.08  # 大幅增加海流耦合系数，确保轨迹明显受海流影响
    dydt[0] += current_coupling * current[0]  # 海流对纵向速度的影响
    dydt[1] += current_coupling * current[1]  # 海流对横向速度的影响
    dydt[2] += current_coupling * current[2]  # 海流对垂向速度的影响

    # 运动学（用合速度）
    dydt[5] =  np.cos(psi) * np.cos(theta) * u_total - np.sin(psi) * v_total + np.cos(psi) * np.sin(theta) * w_total
    dydt[6] =  np.sin(psi) * np.cos(theta) * u_total + np.cos(psi) * v_total + np.sin(theta) * np.sin(psi) * w_total
    dydt[7] = -np.sin(theta) * u_total + np.cos(theta) * w_total
    dydt[8] = q

    # ✅ 保护 theta 附近 ±90° 造成的除以 0 问题
    epsilon = 1e-6
    if np.abs(np.cos(theta)) < epsilon:
        dydt[9] = 0.0
    else:
        dydt[9] = r / np.cos(theta)

    # ✅ 检查 dydt 是否存在异常
    if np.any(np.isnan(dydt)) or np.any(np.isinf(dydt)):
        print("❌ 导数计算异常")
        print("当前状态 x:", x)
        print("current:", current)
        print("theta:", theta, "cos(theta):", np.cos(theta))
        print("dydt:", dydt)

    return dydt
