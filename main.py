import numpy as np
import matplotlib.pyplot as plt
from AdjustAngle import AdjustAngle
import xarray as xr
from ocean_field import OceanCurrentField
from infante import Infante_3d_with_current
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
from limitmaxmin import LimitMaxMin
from scipy.interpolate import RegularGridInterpolator
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# --- 加载海流数据 ---（保持原始顺序）
ds = xr.open_dataset("D:/ocean_data/cmems_mod_glo_phy_my_0.083deg_P1D-m_1751179968305.nc")
uo = ds['uo'].isel(time=0).values  # 形状应为 (depth, lat, lon)
print("提取后 uo shape:", uo.shape)

print(ds['uo'].dims)


vo = ds['vo'].isel(time=0).values
lat = ds['latitude'].values
lon = ds['longitude'].values
depth = ds['depth'].values
print(f"海图纬度范围: {lat.min()} ~ {lat.max()}")
print(f"海图经度范围: {lon.min()} ~ {lon.max()}")
print(f"海图深度范围: {depth.min()} ~ {depth.max()}")

# 不要转置，直接使用原始的 uo, vo 数据
uo_interp = RegularGridInterpolator((depth, lat, lon), uo)
vo_interp = RegularGridInterpolator((depth, lat, lon), vo)

# 构建插值器：注意维度顺序 (depth, lat, lon)
# 调整维度为 (lat, lon, depth)
uo_corrected = uo
vo_corrected = vo
# 构建插值器：注意维度顺序 (depth, lat, lon)
uo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),  # 深度倒序使其从浅到深
    uo_corrected[::-1, :, :]  # 数据也同步倒序
)
vo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),
    vo_corrected[::-1, :, :]
)

print("uo shape:", uo.shape)  # 应该是 (13, x, x)
# 取一个固定位置，检查不同深度上的流速变化
lat_idx = 6  # 中间纬度
lon_idx = 6  # 中间经度

print("\n=== 检查不同深度的流速变化 ===")
for i, d in enumerate(depth):
  print(f"Depth = {d:.1f}m: uo = {uo[i, lat_idx, lon_idx]:.4f}, vo = {vo[i, lat_idx, lon_idx]:.4f}")


print("depth:", depth.shape)  # 应该是 (13,)
print("lat:", lat.shape)
print("lon:", lon.shape)


# 初始化插值器
ocean = OceanCurrentField(uo_corrected, vo_corrected, lat, lon, depth)

# 初始经纬度（例如南海）
lat0 = 19.5  # 改为经纬度范围的中间值 (19°-20°的中间)

lon0 = 115.5  # 改为经纬度范围的中间值 (115°-116°的中间)


# 角度转换常数
R2D = 180 / np.pi


D2R = np.pi / 180


# 辅助函数
def LimitMaxMin(value, max_val, min_val):
    return max(min(value, max_val), min_val)
def xy_to_latlon(x, y, lat0, lon0):
    R = 6371000
    dlat = y / R
    dlon = x / (R * np.cos(np.radians(lat0)))
    return lat0 + np.degrees(dlat), lon0 + np.degrees(dlon)



def AdjustAngle(angle):
    """将角度调整到[-π, π]范围内"""
    while angle > np.pi:
        angle -= 2 * np.pi
    while angle < -np.pi:
        angle += 2 * np.pi
    return angle


# 主函数
def main():
    # 参数初始化
    h = 0.1  # 时间步长 (秒)
    m = 40000 # 时间步数

    # 初始化状态变量
    x = np.zeros(10)
    T = np.zeros(m)
    Y = np.zeros((m, 10))

    # 初始条件
    u0, v0, w0, q0, r0 = 0, 0, 0, 0, 0
    x0, y0, z0, theta0, psi0 = 0, 0, 0, 0, 0
    Initial = np.array([u0, v0, w0, q0, r0, x0, y0, z0, theta0, psi0])
    x = Initial.copy()

    # 控制输入记录
    Force0 = []
    deltav0 = []
    deltah0 = []

    # 假设的动力学模型函数 (需要根据实际模型实现)
   # def Infante_3d(t, x, control):
        # 这里应实现实际的水下航行器动力学模型
        # 返回状态导数 dx/dt
       # return np.zeros_like(x)

    # 主循环
    for i in range(m):
        t = h * (i + 1)
        T[i] = t
        # 当前坐标（根据累积位置计算当前经纬度）
        lat_now = lat0 + x[6] / 111000
        lon_now = lon0 + x[5] / (111000 * np.cos(lat0 * np.pi / 180))
        depth_now = -x[7]  # 注意 z 是负的
        
        # 边界检查改进：限制在数据有效范围内，并添加更大边距
        lat_margin = 0.05  # 增加边距
        lon_margin = 0.05
        depth_margin = 5.0
        
        # 检查是否接近边界，并在接近时提前调整位置
        is_inside = (
            depth.min() + depth_margin <= depth_now <= depth.max() - depth_margin and
            lat.min() + lat_margin <= lat_now <= lat.max() - lat_margin and
            lon.min() + lon_margin <= lon_now <= lon.max() - lon_margin
        )
        
        if not is_inside:
            # 限制在数据有效范围内，避免超出边界
            lat_now = np.clip(lat_now, lat.min() + lat_margin, lat.max() - lat_margin)
            lon_now = np.clip(lon_now, lon.min() + lon_margin, lon.max() - lon_margin)
            depth_now = np.clip(depth_now, depth.min() + depth_margin, depth.max() - depth_margin)
            if i % 500 == 0:
                print(f"注意：在步骤 {i} 处限制了位置以避免超出数据范围")

        # 获取当前位置插值的海流速度
        position_point = [depth_now, lat_now, lon_now]
        
        try:
            u_c, v_c = ocean.get_current(depth_now, lat_now, lon_now)
            
            # 检查返回的海流值是否有效
            if np.isnan(u_c) or np.isnan(v_c):
                # 如果是NaN，尝试使用相邻点的值
                nearby_depths = [depth_now - 1, depth_now + 1]
                nearby_lats = [lat_now - 0.01, lat_now + 0.01]
                nearby_lons = [lon_now - 0.01, lon_now + 0.01]
                
                # 尝试多个相邻点的组合
                for d in nearby_depths:
                    for la in nearby_lats:
                        for lo in nearby_lons:
                            try:
                                temp_u, temp_v = ocean.get_current(d, la, lo)
                                if not (np.isnan(temp_u) or np.isnan(temp_v)):
                                    u_c, v_c = temp_u, temp_v
                                    if i % 500 == 0:
                                        print(f"使用相邻点 ({d:.1f}m, {la:.4f}, {lo:.4f}) 的海流值")
                                    break
                            except:
                                continue
                
                # 如果所有相邻点都失败，使用零值
                if np.isnan(u_c) or np.isnan(v_c):
                    if i % 500 == 0:
                        print(f"无法获取有效海流值，使用零值替代 (步骤 {i})")
                    u_c, v_c = 0.0, 0.0
            
            u_c = float(u_c)
            v_c = float(v_c)
            w_c = 0.0
        except Exception as e:
            if i % 500 == 0:  # 减少输出频率，只在500步的倍数时输出
                print(f"海流插值异常（步骤 {i}）：使用零值替代")
            u_c, v_c, w_c = 0.0, 0.0, 0.0

        current_vector = [u_c, v_c, w_c]  # 三维海流向量

        if i % 500 == 0:  # 每隔500步打印一次
            lat_print, lon_print = xy_to_latlon(x[5], x[6], lat0, lon0)
            print(
                f"[Step {i}] Lat: {lat_print:.4f}, Lon: {lon_print:.4f}, Depth: {-x[7]:.1f}, u_c: {u_c:.4f}, v_c: {v_c:.4f}")
        if np.any(np.isnan(x)):
            print(f"⚠️  第 {i} 步 x 出现 NaN，终止仿真")
            break

        # 控制输入
        Fu = 100 + 20 * np.sin(t/1000)  # 添加周期性变化，模拟推力变化
        deltav = 0  # 水平舵角
        deltah = -10 * np.pi / 180 # 垂直舵角 (转换为弧度)

        # 记录控制输入
        Force0.append(Fu)
        deltav0.append(deltav)
        deltah0.append(deltah)



        # ✅ 正确写法
        k1 = Infante_3d_with_current(t, x, [Fu, deltav, deltah], current_vector)
        k2 = Infante_3d_with_current(t + h / 2, x + h / 2 * k1, [Fu, deltav, deltah], current_vector)
        k3 = Infante_3d_with_current(t + h / 2, x + h / 2 * k2, [Fu, deltav, deltah], current_vector)
        k4 = Infante_3d_with_current(t + h, x + h * k3, [Fu, deltav, deltah], current_vector)

        x = x + h / 6 * (k1 + 2 * k2 + 2 * k3 + k4)
        x[5] = LimitMaxMin(x[5], 1e5, -1e5)  # x 方向
        x[6] = LimitMaxMin(x[6], 1e5, -1e5)  # y 方向
        x[7] = LimitMaxMin(x[7], 0, -2000)  # z 方向（注意是负的）

        # 状态限制
        x[0] = LimitMaxMin(x[0], 4.0, 0)  # u 速度限制增加到4
        x[1] = LimitMaxMin(x[1], 1.5, -1.5)  # v 速度限制增加到±1.5
        x[2] = LimitMaxMin(x[2], 1.5, -1.5)  # w 速度限制增加到±1.5
        x[3] = LimitMaxMin(x[3], 0.1, -0.1)  # q
        x[4] = LimitMaxMin(x[4], 0.1, -0.1)  # r

        x[8] = LimitMaxMin(x[8], 20 * D2R, -20 * D2R)  # theta
        x[9] = AdjustAngle(x[9])  # psi

        # 记录状态
        Y[i] = x
        if i % 500 == 0:  # 每隔500步打印一次
            print(
                f"[Step {i}] Lat: {lat_now:.2f}, Lon: {lon_now:.2f}, Depth: {depth_now:.1f}, u_c: {u_c:.4f}, v_c: {v_c:.4f}")
    print(f"仿真结束，共运行 {i + 1} 步")
    # 提取结果
    U = Y[:, 0]
    V = Y[:, 1]
    W = Y[:, 2]
    Q = Y[:, 3]
    R = Y[:, 4]
    Xp = Y[:, 5]
    Yp = Y[:, 6]
    Zp = Y[:, 7]
    theta = Y[:, 8]
    psi = Y[:, 9]
    
    # 计算总速度和平均速度
    total_speed = np.sqrt(U**2 + V**2 + W**2)
    avg_speed = np.mean(total_speed)
    max_speed = np.max(total_speed)
    final_speed = total_speed[-1]
    
    print(f"\n=== 滑翔机速度分析 ===")
    print(f"最终速度: {final_speed:.2f} m/s ({final_speed*3.6:.2f} km/h)")
    print(f"平均速度: {avg_speed:.2f} m/s ({avg_speed*3.6:.2f} km/h)")
    print(f"最大速度: {max_speed:.2f} m/s ({max_speed*3.6:.2f} km/h)")
    print(f"最终三轴速度: u={U[-1]:.2f} m/s, v={V[-1]:.2f} m/s, w={W[-1]:.2f} m/s")
    
    # 添加海流影响分析
    plt.figure(7, figsize=(12, 10))
    plt.subplot(3, 1, 1)
    plt.plot(T, total_speed, 'r-', linewidth=2, label='总速度')
    plt.xlabel('仿真时间[s]')
    plt.ylabel('速度[m/s]')
    plt.title('滑翔机速度随时间变化')
    plt.legend()
    plt.grid(True)
    
    # 创建一个数组存储海流速度
    current_u = np.zeros(m)
    current_v = np.zeros(m)
    
    # 重新计算每个点的海流速度
    for i in range(m):
        lat_i = lat0 + Y[i, 6] / 111000
        lon_i = lon0 + Y[i, 5] / (111000 * np.cos(lat0 * np.pi / 180))
        depth_i = -Y[i, 7]
        
        try:
            u_c, v_c = ocean.get_current(depth_i, lat_i, lon_i)
            if np.isnan(u_c) or np.isnan(v_c):
                u_c, v_c = 0.0, 0.0
            current_u[i] = u_c
            current_v[i] = v_c
        except:
            current_u[i] = 0.0
            current_v[i] = 0.0
    
    current_speed = np.sqrt(current_u**2 + current_v**2)
    
    plt.subplot(3, 1, 2)
    plt.plot(T, current_speed, 'b-', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('速度[m/s]')
    plt.title('海流速度随时间变化')
    plt.grid(True)
    
    plt.subplot(3, 1, 3)
    plt.plot(T, U, 'g-', linewidth=2, label='u速度')
    plt.plot(T, V, 'b-', linewidth=2, label='v速度')
    plt.plot(T, W, 'r-', linewidth=2, label='w速度')
    plt.xlabel('仿真时间[s]')
    plt.ylabel('速度分量[m/s]')
    plt.title('滑翔机各方向速度分量')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()

    # 绘制结果
    plt.figure(1, figsize=(10, 8))
    plt.subplot(3, 1, 1)
    plt.plot(T, U, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('纵向速度u[m/s]')
    plt.grid(True)

    plt.subplot(3, 1, 2)
    plt.plot(T, V, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('横向速度v[m/s]')
    plt.grid(True)

    plt.subplot(3, 1, 3)
    plt.plot(T, W, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('垂向速度w[m/s]')
    plt.grid(True)
    plt.tight_layout()

    plt.figure(2, figsize=(10, 8))
    plt.subplot(3, 1, 1)
    plt.plot(T, Xp, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('纵向位移[m]')
    plt.grid(True)

    plt.subplot(3, 1, 2)
    plt.plot(T, Yp, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('横向位移[m]')
    plt.grid(True)

    plt.subplot(3, 1, 3)
    plt.plot(T, Zp, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('垂向位移[m]')
    plt.grid(True)
    plt.tight_layout()

    fig3 = plt.figure(3, figsize=(10, 8))
    ax = fig3.add_subplot(111, projection='3d')
    ax.plot(Xp, Yp, Zp, 'b--', linewidth=2)
    ax.set_xlabel('纵向位移[m]')
    ax.set_ylabel('横向位移[m]')
    ax.set_zlabel('垂向位移[m]')
    ax.set_title('矢量航行器空间运动航迹')
    plt.grid(True)

    plt.figure(4, figsize=(10, 8))
    plt.subplot(2, 1, 1)
    plt.plot(T, psi * R2D, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('艏向角[deg]')
    plt.grid(True)

    plt.subplot(2, 1, 2)
    plt.plot(T, theta * R2D, 'b', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('纵倾角[deg]')
    plt.grid(True)
    plt.tight_layout()

    plt.figure(5, figsize=(10, 8))
    plt.subplot(2, 1, 1)
    plt.plot(Xp, Yp, 'b--', linewidth=2)
    plt.xlabel('纵向位移[m]')
    plt.ylabel('横向位移[m]')
    plt.title('UUV航迹')
    plt.grid(True)

    plt.subplot(2, 1, 2)
    plt.plot(Xp, Zp, 'b--', linewidth=2)
    plt.xlabel('纵向位移[m]')
    plt.ylabel('垂向位移[m]')
    plt.title('UUV航迹')
    plt.grid(True)
    plt.tight_layout()

    # 添加总速度图
    plt.figure(6, figsize=(10, 6))
    plt.plot(T, total_speed, 'r-', linewidth=2)
    plt.xlabel('仿真时间[s]')
    plt.ylabel('总速度[m/s]')
    plt.title('滑翔机总速度变化曲线')
    plt.grid(True)
    plt.tight_layout()

    plt.show()


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print("程序发生异常：", e)
