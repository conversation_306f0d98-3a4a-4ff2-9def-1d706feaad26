import numpy as np
import matplotlib
# 使用默认后端，允许图形窗口弹出
import matplotlib.pyplot as plt
from AdjustAngle import AdjustAngle
import xarray as xr
from ocean_field import OceanCurrentField
from infante import Infante_3d_with_current
from mpl_toolkits.mplot3d import Axes3D
from limitmaxmin import LimitMaxMin
from scipy.interpolate import RegularGridInterpolator
from matplotlib.cm import get_cmap
from pyproj import Geod
from scipy.ndimage import zoom
import time
import os
import datetime
import matplotlib.patches as patches
import pandas as pd
import networkx as nx  # 用于处理网络拓扑结构
import matplotlib.animation as animation
from scipy.integrate import solve_ivp
import traceback  # 添加traceback模块导入
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# --- 加载海流数据 ---
ds = xr.open_dataset("D:/ocean_data/cmems_mod_glo_phy_my_0.083deg_P1D-m_1751179968305.nc")
uo = ds['uo'].isel(time=0).values  # 形状应为 (depth, lat, lon)
print("提取后 uo shape:", uo.shape)

print(ds['uo'].dims)

vo = ds['vo'].isel(time=0).values
lat = ds['latitude'].values
lon = ds['longitude'].values
depth = ds['depth'].values
print(f"海图纬度范围: {lat.min()} ~ {lat.max()}")
print(f"海图经度范围: {lon.min()} ~ {lon.max()}")
print(f"海图深度范围: {depth.min()} ~ {depth.max()}")

# 构建插值器：注意维度顺序 (depth, lat, lon)
uo_corrected = uo
vo_corrected = vo
uo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),  # 深度倒序使其从浅到深
    uo_corrected[::-1, :, :]  # 数据也同步倒序
)
vo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),
    vo_corrected[::-1, :, :]
)

print("uo shape:", uo.shape)  # 应该是 (13, x, x)
# 取一个固定位置，检查不同深度上的流速变化
lat_idx = 6  # 中间纬度
lon_idx = 6  # 中间经度

print("\n=== 检查不同深度的流速变化 ===")
for i, d in enumerate(depth):
    print(f"Depth = {d:.1f}m: uo = {uo[i, lat_idx, lon_idx]:.4f}, vo = {vo[i, lat_idx, lon_idx]:.4f}")

print("depth:", depth.shape)  # 应该是 (13,)
print("lat:", lat.shape)
print("lon:", lon.shape)

# 初始化插值器
ocean = OceanCurrentField(uo_corrected, vo_corrected, lat, lon, depth)

# 将初始位置移到图像中心
lat0 = (lat[0] + lat[-1]) / 2  # 使用纬度的中点
lon0 = (lon[0] + lon[-1]) / 2  # 使用经度的中点

# 角度转换常数
R2D = 180 / np.pi
D2R = np.pi / 180

# 计算两点间的角度（用于计算朝向目标的航向）
def calculate_target_heading(current_x: float, current_y: float, target_x: float, target_y: float) -> float:
    """计算从当前位置指向目标位置的航向角。
    
    Args:
        current_x: 当前x坐标
        current_y: 当前y坐标
        target_x: 目标x坐标
        target_y: 目标y坐标
    
    Returns:
        float: 航向角(弧度)
    """
    dx = target_x - current_x
    dy = target_y - current_y
    return np.arctan2(dy, dx)

# 无波动目标控制函数 - 完全消除目标区域波动
def calculate_no_oscillation_target(current_x, current_y, formation_center_x, formation_center_y,
                                   individual_target_x, individual_target_y, initial_dx, initial_dy,
                                   formation_weight, auv_index, target_reached_flags,
                                   pass_through_mode=True):
    """
    无波动目标计算函数 - 确保AUV到达目标后完全停止波动

    参数:
    current_x, current_y: 当前AUV位置
    formation_center_x, formation_center_y: 编队中心位置
    individual_target_x, individual_target_y: 该AUV的个体目标位置
    initial_dx, initial_dy: 编队中的相对位置
    formation_weight: 编队权重
    auv_index: AUV索引
    target_reached_flags: 目标到达标志数组
    pass_through_mode: 是否允许穿越模式

    返回:
    final_target_x, final_target_y: 最终目标位置
    """

    # 计算到个体目标的距离
    dist_to_target = np.sqrt((current_x - individual_target_x)**2 + (current_y - individual_target_y)**2)

    # 如果已经到达目标（放宽距离条件到1.0km），停止目标追踪
    if dist_to_target <= 1.0:  # 从0.5km放宽到1.0km，更容易到达
        target_reached_flags[auv_index] = True

        # 无论是穿越模式还是停止模式，都让AUV停留在目标位置
        # 这样可以避免折返到(0,0)点的问题
        final_target_x = individual_target_x
        final_target_y = individual_target_y

        return final_target_x, final_target_y

    # 未到达目标时的控制逻辑 - 优化为更直线的路径
    if dist_to_target > 15.0:
        # 距离很远时，优先编队保持，但增加个体目标权重以减少弯曲
        individual_weight = 0.4  # 从0.2增加到0.4
        formation_weight_adj = formation_weight * 0.6  # 从0.8减少到0.6
    elif dist_to_target > 8.0:
        # 中等距离时，大幅增加个体目标权重，减少编队干扰
        individual_weight = 0.8  # 从0.7增加到0.8
        formation_weight_adj = formation_weight * 0.2  # 从0.3减少到0.2
    elif dist_to_target > 3.0:
        # 接近目标时，几乎完全专注个体目标
        individual_weight = 0.95  # 新增阶段
        formation_weight_adj = formation_weight * 0.05
    else:
        # 非常接近目标时，完全专注个体目标，消除编队干扰
        individual_weight = 1.0
        formation_weight_adj = 0.0

    # 计算编队目标位置
    ideal_center_x = formation_center_x
    ideal_center_y = formation_center_y
    formation_target_x = ideal_center_x + initial_dx * formation_weight_adj
    formation_target_y = ideal_center_y + initial_dy * formation_weight_adj

    # 混合编队目标和个体目标 - 使用更直接的路径
    final_target_x = formation_target_x * (1.0 - individual_weight) + individual_target_x * individual_weight
    final_target_y = formation_target_y * (1.0 - individual_weight) + individual_target_y * individual_weight

    return final_target_x, final_target_y

# 柔性多体动力学相关函数
def create_formation_graph(num_auvs, topology_type='hierarchical'):
    """创建编队拓扑结构图，支持多层次异构连接"""
    G = nx.Graph()
    
    # 添加节点
    for i in range(num_auvs):
        G.add_node(i)
    
    # 根据拓扑类型添加边
    if topology_type == 'complete':
        # 完全图 - 每个AUV与其他所有AUV相连
        for i in range(num_auvs):
            for j in range(i+1, num_auvs):
                G.add_edge(i, j, type='global', weight=0.5)
    
    elif topology_type == 'ring':
        # 环形 - 每个AUV只与相邻的两个AUV相连
        for i in range(num_auvs):
            G.add_edge(i, (i+1) % num_auvs, type='local', weight=1.0)
    
    elif topology_type == 'star':
        # 星形 - 中心AUV与所有其他AUV相连
        for i in range(1, num_auvs):
            G.add_edge(0, i, type='global', weight=1.0)
    
    elif topology_type == 'mesh':
        # 网格 - 每个AUV与最近的几个AUV相连
        for i in range(num_auvs):
            for j in range(i+1, min(i+4, num_auvs)):
                G.add_edge(i, j, type='local', weight=1.0)
            if i >= 3:  # 添加一些交叉连接
                G.add_edge(i, i-3, type='diagonal', weight=0.7)
    
    elif topology_type == 'hierarchical':
        # 分层次结构 - 结合局部强连接和全局弱连接
        
        # 1. 添加强局部环形连接
        for i in range(num_auvs):
            G.add_edge(i, (i+1) % num_auvs, type='local', weight=1.0)
            
        # 2. 添加次强对角连接
        for i in range(num_auvs):
            G.add_edge(i, (i+2) % num_auvs, type='diagonal', weight=0.7)
            
        # 3. 添加弱全局连接
        for i in range(num_auvs):
            for j in range(i+3, i+int(num_auvs/2), 2):
                G.add_edge(i, j % num_auvs, type='global', weight=0.3)
                
        # 4. 为高度连接性，确保至少一个远距离连接
        for i in range(0, num_auvs, 2):
            opposite = (i + num_auvs//2) % num_auvs
            G.add_edge(i, opposite, type='global', weight=0.4)
    
    elif topology_type == 'adaptive':
        # 自适应拓扑 - 基础连接为局部环形，然后根据需要动态调整
        # 初始连接为环形+少量全局连接
        for i in range(num_auvs):
            G.add_edge(i, (i+1) % num_auvs, type='local', weight=1.0)
            
        # 添加几个跨距离连接以增强鲁棒性
        for i in range(0, num_auvs, 3):
            G.add_edge(i, (i + num_auvs//2) % num_auvs, type='global', weight=0.4)
    
    # 打印拓扑统计
    local_edges = sum(1 for _, _, d in G.edges(data=True) if d.get('type') == 'local')
    diagonal_edges = sum(1 for _, _, d in G.edges(data=True) if d.get('type') == 'diagonal')
    global_edges = sum(1 for _, _, d in G.edges(data=True) if d.get('type') == 'global')
    
    print(f"拓扑结构: {topology_type}, 总连接数: {G.number_of_edges()}")
    print(f"局部连接: {local_edges}, 对角连接: {diagonal_edges}, 全局连接: {global_edges}")
    
    return G

def get_adaptive_stiffness(formation_graph, relative_position, current_strength, base_stiffness=200.0, current_threshold=0.5):
    """
    根据拓扑结构、相对位置和海流强度调整弹簧刚度
    
    参数:
    formation_graph - 表示AUV连接关系的图
    relative_position - 相对位置
    current_strength - 海流强度
    base_stiffness - 基础刚度系数
    current_threshold - 海流强度阈值
    
    返回:
    connection_stiffness - 边的刚度字典
    """
    # 初始化连接刚度字典
    connection_stiffness = {}
    
    # 获取基本刚度参数
    k_local = base_stiffness * 1.5  # 局部连接刚度增强
    k_diagonal = base_stiffness * 1.0  # 对角连接基准刚度
    k_global = base_stiffness * 0.7  # 全局连接刚度降低
    
    # 遍历所有边，根据类型设置基础刚度
    for i, j, data in formation_graph.edges(data=True):
        conn_type = data.get('type', 'local')
        weight = data.get('weight', 1.0)
        
        if conn_type == 'local':
            conn_stiffness = k_local * weight
        elif conn_type == 'diagonal':
            conn_stiffness = k_diagonal * weight
        else:  # global
            conn_stiffness = k_global * weight
        
        # 根据当前海流强度调整刚度
        # 当海流强，增加刚度以抵抗形变；当海流弱，降低刚度增加灵活性
        current_factor = 1.0 + max(0, current_strength - current_threshold) / current_threshold
        conn_stiffness *= current_factor
        
        # 存储边刚度
        connection_stiffness[(i, j)] = conn_stiffness
        connection_stiffness[(j, i)] = conn_stiffness  # 确保无向图对称性
    
    return connection_stiffness

def calculate_velocity_consensus(velocities, formation_graph, consensus_gain=0.15):
    """
    计算速度共识项，使AUV群体速度趋于一致
    
    参数:
    velocities - AUV速度列表
    formation_graph - 表示AUV连接关系的图
    consensus_gain - 共识增益系数
    
    返回:
    consensus_forces - 每个AUV的共识力
    """
    num_auvs = len(velocities)
    consensus_forces = [np.zeros(3) for _ in range(num_auvs)]
    
    # 计算平均速度（全局共识目标）
    mean_velocity = np.mean(velocities, axis=0)
    mean_speed = np.linalg.norm(mean_velocity)
    
    # 计算每个AUV的速度与邻居平均速度的差异
    for i in range(num_auvs):
        # 获取邻居节点
        neighbors = list(formation_graph.neighbors(i))
        
        if neighbors:
            # 计算邻居平均速度
            neighbor_velocities = [velocities[j] for j in neighbors]
            neighbor_mean = np.mean(neighbor_velocities, axis=0)
            
            # 获取当前AUV的速度模值
            current_speed = np.linalg.norm(velocities[i])
            
            # 速度差异太大时增强共识力
            speed_diff_ratio = abs(current_speed - mean_speed) / (mean_speed + 0.1)  # 避免除以零
            adaptive_factor = 1.0 + min(1.5, speed_diff_ratio)  # 限制最大增强倍数为2.5倍
            
            # 加权混合局部共识(邻居平均)和全局共识(整体平均)
            # 如果速度差异大，则更偏向全局共识
            local_weight = 0.7
            if speed_diff_ratio > 0.4:  # 速度差异超过40%时调整权重
                local_weight = max(0.3, 0.7 - speed_diff_ratio * 0.5)  # 最低减到0.3
                
            local_diff = neighbor_mean - velocities[i]
            global_diff = mean_velocity - velocities[i]
            
            # 速度共识力 (动态调整局部和全局权重)
            consensus_forces[i] = consensus_gain * adaptive_factor * (local_weight * local_diff + (1.0 - local_weight) * global_diff)
    
    return consensus_forces

def calculate_spring_damper_forces(positions, velocities, initial_formation, formation_graph, 
                                  spring_stiffness, damping_coefficient, current_vectors=None):
    """
    计算虚拟弹簧和阻尼力，增强型自适应版本
    
    参数:
    positions - AUV位置列表，每个元素为[x, y, z]
    velocities - AUV速度列表，每个元素为[u, v, w]
    initial_formation - 初始编队相对位置
    formation_graph - 表示AUV连接关系的图
    spring_stiffness - 弹簧刚度系数k (基础值)
    damping_coefficient - 阻尼系数c (基础值)
    current_vectors - 海流向量列表 (可选)
    
    返回:
    flexible_forces - 每个AUV的柔性力，列表，每个元素为[Fx, Fy, Fz]
    spring_energies - 每对连接的弹簧势能
    damping_powers - 每对连接的阻尼功率
    """
    num_auvs = len(positions)
    flexible_forces = [np.zeros(3) for _ in range(num_auvs)]
    spring_energies = {}
    damping_powers = {}
    
    # 计算编队中心
    center_pos = np.mean(positions, axis=0)
    
    # 如果提供了海流数据，计算平均海流强度
    current_strength = 0.0
    if current_vectors is not None:
        current_norms = [np.linalg.norm(cv) for cv in current_vectors]
        current_strength = np.mean(current_norms)
    
    # 获取自适应刚度
    connection_stiffness = get_adaptive_stiffness(formation_graph, initial_formation, 
                                                current_strength, spring_stiffness)
    
    # 计算速度共识力
    consensus_forces = calculate_velocity_consensus(velocities, formation_graph)
    
    # 计算全局速度一致性指标
    speeds = np.array([np.linalg.norm(v) for v in velocities])
    speed_std = np.std(speeds)
    speed_mean = np.mean(speeds)
    
    # 高速度差异时增加阻尼
    speed_variation_factor = 1.0
    if speed_mean > 0.3:  # 只在有意义的速度下考虑
        speed_cv = speed_std / speed_mean  # 速度变异系数
        if speed_cv > 0.3:  # 速度变异大时增加阻尼
            speed_variation_factor = 1.0 + min(0.8, (speed_cv - 0.3) * 2.0)  # 最多增加到1.8倍
    
    # 遍历所有边（AUV间的连接）
    for i, j, edge_data in formation_graph.edges(data=True):
        # 相对位置向量
        r_ij = positions[i] - positions[j]
        r_norm = np.linalg.norm(r_ij)
        
        # 计算期望距离（根据初始编队形状）
        initial_pos_i = initial_formation[i]
        initial_pos_j = initial_formation[j]
        desired_dist = np.linalg.norm(initial_pos_i - initial_pos_j)
        
        # 单位方向向量
        if r_norm > 0:
            r_unit = r_ij / r_norm
        else:
            r_unit = np.array([0, 0, 0])
        
        # 相对速度向量
        v_ij = velocities[i] - velocities[j]
        
        # 获取该连接的自适应刚度
        edge_stiffness = connection_stiffness.get((i,j), spring_stiffness)
        
        # 连接类型与权重
        conn_type = edge_data.get('type', 'local')
        weight = edge_data.get('weight', 1.0)
        
        # 根据连接类型调整阻尼系数
        edge_damping = damping_coefficient
        if conn_type == 'local':
            edge_damping *= 1.1  # 局部连接适度增强阻尼
        elif conn_type == 'global':
            edge_damping *= 0.9  # 全局连接降低阻尼
            
        # 应用速度变异系数增强阻尼
        edge_damping *= speed_variation_factor
        
        # 计算弹簧力（增强型Hooke定律）
        # 1. 线性项 - 经典Hooke定律
        linear_term = -edge_stiffness * (r_norm - desired_dist)
        
        # 2. 非线性项 - 大变形抵抗（队形稳定性增强）- 使用更平滑的二次函数
        deform_ratio = abs(r_norm - desired_dist) / (desired_dist + 0.01)  # 避免除零
        nonlinear_term = 0
        if deform_ratio > 0.3:  # 超过30%变形启用非线性增强
            # 使用更平滑的二次函数而不是阶跃变化
            nonlinear_factor = (deform_ratio - 0.3) ** 2
            nonlinear_term = -edge_stiffness * nonlinear_factor * 0.8 * np.sign(r_norm - desired_dist)
        
        # 合成弹簧力
        spring_force = (linear_term + nonlinear_term) * r_unit * weight
        
        # 计算阻尼力（Rayleigh阻尼）- 加入速度方向分量
        v_proj = np.dot(v_ij, r_unit) * r_unit  # 速度在连接方向上的投影
        v_perp = v_ij - v_proj  # 垂直于连接方向的速度分量
        
        # 分别阻尼处理两个方向的分量（径向阻尼更强）
        damping_force_proj = -edge_damping * 1.2 * v_proj  # 径向阻尼增强20%
        damping_force_perp = -edge_damping * 0.9 * v_perp  # 切向阻尼降低10%
        damping_force = (damping_force_proj + damping_force_perp) * weight
        
        # 合成柔性力
        flexible_force = spring_force + damping_force
        
        # 力的作用与反作用
        flexible_forces[i] += flexible_force
        flexible_forces[j] -= flexible_force
        
        # 计算弹簧势能
        spring_energy = 0.5 * edge_stiffness * (r_norm - desired_dist)**2 * weight
        spring_energies[(i, j)] = spring_energy
        
        # 计算阻尼功率（负值表示能量耗散）
        damping_power = edge_damping * np.linalg.norm(v_ij)**2 * weight
        damping_powers[(i, j)] = damping_power
    
    # 添加速度共识力到总柔性力
    for i in range(num_auvs):
        flexible_forces[i] += consensus_forces[i]
    
    return flexible_forces, spring_energies, damping_powers

def calculate_energy_metrics(positions, velocities, flexible_forces, spring_energies, damping_powers, 
                           thrust_forces, current_vectors, mass=500):
    """
    计算能量分析指标
    
    参数:
    positions - AUV位置列表
    velocities - AUV速度列表
    flexible_forces - 柔性力列表
    spring_energies - 弹簧势能字典
    damping_powers - 阻尼功率字典
    thrust_forces - 推力列表
    current_vectors - 海流向量列表
    mass - AUV质量
    
    返回:
    energy_metrics - 包含各种能量指标的字典
    """
    num_auvs = len(positions)
    
    # 计算每个AUV的动能
    kinetic_energies = [0.5 * mass * np.linalg.norm(v)**2 for v in velocities]
    
    # 计算总动能
    kinetic_energy = sum(kinetic_energies)
    
    # 计算弹簧总势能
    total_spring_energy = sum(spring_energies.values())
    
    # 计算阻尼总功率
    total_damping_power = sum(damping_powers.values())
    
    # 计算每个AUV的推力做功
    thrust_powers = [np.dot(thrust_forces[i], velocities[i]) for i in range(num_auvs)]
    thrust_power = sum(thrust_powers)
    
    # 计算每个AUV的海流能量交换
    current_powers = [mass * np.dot(current_vectors[i], velocities[i]) for i in range(num_auvs)]
    current_power = sum(current_powers)
    
    # 计算柔性力做功
    flex_powers = [np.dot(flexible_forces[i], velocities[i]) for i in range(num_auvs)]
    flex_power = sum(flex_powers)
    
    # 系统能量变化率
    energy_rate = thrust_power + current_power - total_damping_power
    
    # 返回所有能量指标
    energy_metrics = {
        'kinetic_energy': kinetic_energy,
        'kinetic_energies': kinetic_energies,
        'potential_energy': total_spring_energy,
        'damping_power': total_damping_power,
        'thrust_power': thrust_power,
        'thrust_powers': thrust_powers,
        'current_power': current_power,
        'current_powers': current_powers,
        'flex_power': flex_power,
        'energy_rate': energy_rate,
        'total_power': energy_rate
    }
    
    return energy_metrics

# 状态保存函数
def save_simulation_state(filename, step, T, all_Y, current_states, formation_center_x, formation_center_y, 
                         reached_target, target_reached_time, all_current_effects, initial_formation, 
                         final_formation, energy_data=None):
    import pickle
    state = {
        'step': step,
        'T': T,
        'all_Y': all_Y,
        'current_states': current_states,
        'formation_center_x': formation_center_x,
        'formation_center_y': formation_center_y,
        'reached_target': reached_target,
        'target_reached_time': target_reached_time,
        'all_current_effects': all_current_effects,
        'initial_formation': initial_formation,
        'final_formation': final_formation,
        'energy_data': energy_data  # 添加能量数据
    }
    with open(filename, 'wb') as f:
        pickle.dump(state, f)
    print(f"保存仿真状态到 {filename}")

# 创建输出目录函数
def create_output_directory():
    # 创建一个基于日期时间的输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"simulation_output_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

# 状态加载函数
def load_simulation_state(filename):
    import pickle
    with open(filename, 'rb') as f:
        state = pickle.load(f)
    print(f"从 {filename} 加载仿真状态")
    return state

# 添加状态平滑过渡函数
def smooth_transition(value, target, smoothing_factor=0.05):
    """平滑过渡函数，避免状态突变"""
    return value + smoothing_factor * (target - value)

# 添加S形过渡函数
def s_curve_transition(x, x_min, x_max, y_min, y_max):
    """S形过渡函数，在x_min和x_max之间实现y_min到y_max的平滑过渡"""
    if x <= x_min:
        return y_min
    if x >= x_max:
        return y_max
    # 归一化x到[0,1]区间
    t = (x - x_min) / (x_max - x_min)
    # S形函数: 3t²-2t³，比线性过渡更平滑
    s = 3 * t * t - 2 * t * t * t
    # 映射到[y_min, y_max]
    return y_min + s * (y_max - y_min)

# 目标区域稳定控制函数 - 支持穿越目标
def apply_target_area_stabilization(x, target_x, target_y, target_radius, dist_to_target, allow_pass_through=True):
    """
    在目标区域内应用特殊的稳定控制策略，支持穿越目标

    参数:
    x - AUV状态向量
    target_x, target_y - 目标位置(km)
    target_radius - 目标区域半径(km)
    dist_to_target - 当前AUV到目标的距离(km)
    allow_pass_through - 是否允许穿越目标区域

    返回:
    x - 修改后的状态向量
    """
    # 如果允许穿越，不应用任何控制，完全不改变状态向量
    if allow_pass_through:
        return x  # 直接返回原始状态向量，不应用任何稳定控制

    else:
        # 原有的强制停留逻辑（保留作为选项）
        if dist_to_target > target_radius * 2.0:
            return x

        x_new = x.copy()

        # 1. 速度限制 - 距离越近，最大允许速度越小
        max_speed_factor = min(1.0, dist_to_target / (target_radius * 1.5))
        max_allowed_speed = 1.0 + max_speed_factor * 1.0  # 1.0 到 2.0 m/s

        current_speed = np.sqrt(x_new[0]**2 + x_new[1]**2)
        if current_speed > max_allowed_speed:
            speed_ratio = max_allowed_speed / current_speed
            x_new[0] *= speed_ratio
            x_new[1] *= speed_ratio

        # 2. 角速度阻尼
        damping_factor = 1.0 - 0.3 * (1.0 - min(1.0, dist_to_target / target_radius))
        x_new[3] *= damping_factor
        x_new[4] *= damping_factor

        # 3. 位置约束
        if dist_to_target > target_radius * 1.1:
            current_x_km = x_new[5] / 1000.0
            current_y_km = x_new[6] / 1000.0

            dir_x = (target_x - current_x_km) / dist_to_target
            dir_y = (target_y - current_y_km) / dist_to_target

            pullback_strength = 0.05 * (dist_to_target - target_radius)

            x_new[0] += pullback_strength * dir_x
            x_new[1] += pullback_strength * dir_y

        return x_new

# 主函数
def main(max_steps=800000, load_state=False, save_results=True, auto_stop=True, target_reach_distance=0.5, 
         use_flexible_model=True, spring_stiffness=180.0, damping_coefficient=150.0, 
         formation_topology='hierarchical', auv_mass=500.0, consensus_gain=0.28,
         data_record_interval=10, skip_plots=False, pass_through=True):
    # 参数初始化
    h_default = 0.05  # 默认步长
    h_min = 0.01      # 最小步长（接近目标区域时使用）
    h = h_default
    m = max_steps  # 可配置仿真步数
    num_auvs = 10  # AUV数量
    
    # 数据记录频率(每N步记录一次数据，加快计算)
    print(f"每{data_record_interval}步记录一次数据，用于加快解算速度")
    
    # 计算相关间隔 (基于数据记录间隔)
    formation_calc_interval = max(5, data_record_interval * 5)  # 编队计算间隔
    velocity_calc_interval = max(10, data_record_interval * 10)  # 速度一致性计算间隔
    progress_output_interval = max(1000, data_record_interval * 200)  # 进度输出间隔
    detail_output_interval = progress_output_interval * 5  # 详细信息输出间隔
    
    print(f"编队计算间隔: {formation_calc_interval}步")
    print(f"速度一致性计算间隔: {velocity_calc_interval}步")
    print(f"进度输出间隔: {progress_output_interval}步")
    
    # 创建输出目录
    output_dir = create_output_directory()
    print(f"所有输出将保存到: {output_dir}")
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lon, lat)
    
    # 使用pyproj计算相对坐标
    geod = Geod(ellps='WGS84')
    center_lat = (lat[0] + lat[-1]) / 2
    center_lon = (lon[0] + lon[-1]) / 2
    
    # 计算每个格点相对于中心的距离
    dx_km = np.zeros_like(lon_grid)
    dy_km = np.zeros_like(lat_grid)
    for i in range(lat_grid.shape[0]):
        for j in range(lat_grid.shape[1]):
            az12, _, dist = geod.inv(center_lon, center_lat, lon_grid[i, j], lat_grid[i, j])
            dx_km[i, j] = dist * np.sin(np.radians(az12)) / 1000
            dy_km[i, j] = dist * np.cos(np.radians(az12)) / 1000
    
    # 平移坐标原点至图左下角
    x_shift, y_shift = dx_km.min(), dy_km.min()
    dx_km -= x_shift
    dy_km -= y_shift

    # 初始化多个AUV (沿圆周排列) - 移到目标区域设置之前
    radius_km = 5  # 圆半径，单位公里

    # 在坐标系中心点附近创建圆形编队
    states = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)

    # 保存初始编队相对位置
    initial_formation = []

    # 设置编队中心位置 (修改为更接近目标)
    center_x = 25  # 原来为20，修改为25使起点更接近目标
    center_y = 15  # 原来为10，修改为15使起点更接近目标

    for angle in angles:
        # 计算圆上的点 - 圆心设置在(center_x, center_y)
        x_p = center_x + radius_km * np.cos(angle)
        y_p = center_y + radius_km * np.sin(angle)

        # 保存初始相对位置（相对于编队中心）
        initial_formation.append([x_p - center_x, y_p - center_y])

        # 设置随机初始航向，不再指向目标
        initial_heading = np.random.uniform(0, 2*np.pi)  # 随机航向

        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        state = np.array([0, 0, 0, 0, 0, x_p * 1000, y_p * 1000, 0, 0, initial_heading])  # 转换为米
        states.append(state)

    initial_states = np.array(states)
    initial_formation = np.array(initial_formation)  # 转换为NumPy数组

    # 目标区域设置 - 与编队大小完全匹配，实现点对点对接
    target_center_x = 35  # 目标区域中心x坐标
    target_center_y = 30  # 目标区域中心y坐标

    # 计算编队的实际半径（基于初始编队的最大距离）
    formation_radius = max([np.sqrt(pos[0]**2 + pos[1]**2) for pos in initial_formation])
    target_radius = formation_radius  # 目标区域半径与编队半径完全相同

    # 为每个AUV创建对应的目标点 - 与编队形状完全匹配
    target_positions = []
    for i in range(num_auvs):
        # 使用与初始编队相同的相对位置，但移动到目标区域
        target_x = target_center_x + initial_formation[i][0]
        target_y = target_center_y + initial_formation[i][1]
        target_positions.append([target_x, target_y])

    target_positions = np.array(target_positions)
    print(f"目标区域设置 - 点对点匹配:")
    print(f"  中心位置: ({target_center_x:.1f}, {target_center_y:.1f}) km")
    print(f"  区域半径: {target_radius:.1f} km (与编队半径匹配)")
    print(f"为{num_auvs}个AUV设置了点对点目标:")
    for i, pos in enumerate(target_positions):
        print(f"  AUV{i+1}: ({pos[0]:.1f}, {pos[1]:.1f}) km")
    
    # 检查自动停止设置
    if auto_stop:
        print(f"自动停止功能已启用: 当所有AUV到达各自目标点(距离 <= 1.0km)时将自动结束仿真")  # 更新为实际使用的距离
    else:
        print("自动停止功能已禁用: 即使到达目标区域也将继续仿真直到最大步数")
    
    # 编队形状保持参数
    formation_keep_distance = 10.0  # 开始强制保持编队形状的距离（km）
    formation_weight = 1.0  # 初始编队保持权重

    # 无波动控制参数
    target_reached_flags = [False] * num_auvs  # 每个AUV的目标到达标志
    pass_through_mode = True  # 穿越模式：True=穿越，False=停止

    # 控制策略阶段
    PHASE_TARGET_TRACKING = 1  # 目标跟踪阶段
    PHASE_FORMATION_ONLY = 2   # 纯编队保持阶段
    control_phase = PHASE_TARGET_TRACKING  # 初始为目标跟踪阶段
    
    # 添加参数说明注释
    print("\n=== 编队稳定性增强版（两阶段控制+平滑过渡）===")
    print("• 自适应步长：近目标区域减小积分步长")
    print("• 能量监控：检测异常能量增长并采取应急措施")
    print("• 两阶段控制：远距离目标跟踪，近距离纯编队保持")
    print("• 弱化刚度：近目标区域平滑降低刚度至50%")
    print("• 增强阻尼：近目标区域平滑增加阻尼至140%")
    print("• 禁用扰动：近目标区域平滑降低随机扰动至20%")
    print("• 优化过渡：采用平滑S型过渡代替阶跃变化")
    print("================================\n")
    
    # 柔性多体动力学参数
    if use_flexible_model:
        print("使用柔性多体动力学模型...")
        # 创建编队拓扑图
        formation_graph = create_formation_graph(num_auvs, topology_type=formation_topology)
        print(f"编队拓扑: {formation_topology}, 连接数: {formation_graph.number_of_edges()}")
    else:
        print("使用标准动力学模型...")
        formation_graph = None
    
    # 创建更加差异化的推力和舵角设置 - 大幅提升推力以获得更高速度
    thrust_settings = np.linspace(450, 600, num_auvs)  # 大幅提升推力范围：从280-380提升到450-600
    # 添加小的随机变化到每个推力设置
    thrust_settings = thrust_settings + np.random.uniform(-15, 15, num_auvs)
    
    # 垂直舵角仍保持为0(保持水平运动)
    deltah_settings = np.zeros(num_auvs)
    
    # 水平舵角初始有微小差异
    deltav_settings = np.random.uniform(-0.02, 0.02, num_auvs)
    
    # 使用彩虹色谱为每个AUV分配不同颜色
    colors = get_cmap('rainbow')(np.linspace(0, 1, num_auvs))
    
    # 定义最大速度限制 (2.0 m/s)
    max_speed = 2.0
    
    # 选取表层海流数据
    uo_plot = uo[0]  # 表层海流 u 分量
    vo_plot = vo[0]  # 表层海流 v 分量

    # 设置不同的初始速度 - 提升初始速度范围
    for i in range(num_auvs):
        # 每个AUV有不同的初始速度(1.2-1.8 m/s范围内) - 大幅提升
        initial_states[i, 0] = 1.5 + np.random.uniform(-0.3, 0.3)

        # 添加小的初始横向速度
        initial_states[i, 1] = np.random.uniform(-0.15, 0.15)
    
    # 为每个AUV创建状态数组
    T = np.zeros(m)
    all_Y = np.zeros((num_auvs, m, 10))  # [AUV编号, 时间步, 状态向量]
    
    # 为每个AUV创建当前状态向量和速度记录
    current_states = initial_states.copy()
    
    # 记录每个AUV在每个时间步的海流影响
    all_current_effects = np.zeros((num_auvs, m, 3))  # [AUV编号, 时间步, [u_c, v_c, 海流强度]]
    
    # 记录编队中心位置
    formation_center_x = np.zeros(m)
    formation_center_y = np.zeros(m)
    
    # 初始化第一个点的编队中心位置
    formation_center_x[0] = center_x
    formation_center_y[0] = center_y
    
    # 记录编队到达目标的时间
    reached_target = False
    target_reached_time = 0
    
    # 记录最终编队形状
    final_formation = np.zeros_like(initial_formation)
    
    # 保存轨迹和指标数据 - 减少数据存储量
    all_data = {
        'time': [],
        'formation_center_x': [],
        'formation_center_y': [],
        'dist_to_target': [],
        'formation_deviation': []
    }
    
    # 能量分析数据
    energy_data = {
        'time': [],
        'kinetic_energy': [],
        'potential_energy': [],
        'damping_energy': [],
        'thrust_work': [],
        'current_exchange': [],
        'total_energy': []
    }
    cumulative_energy = {
        'damping_energy': 0.0,
        'thrust_work': 0.0,
        'current_exchange': 0.0
    }
    
    # 边界设置
    x_boundary_min = 0
    x_boundary_max = dx_km.max()
    y_boundary_min = 0
    y_boundary_max = dy_km.max()
    
    # 主循环 - 时间步
    # 初始化或加载仿真状态
    start_step = 0
    if load_state:
        try:
            # 尝试加载上次仿真状态
            saved_state = load_simulation_state('simulation_state.pkl')
            start_step = saved_state['step'] + 1
            T[:start_step] = saved_state['T'][:start_step]
            all_Y[:, :start_step, :] = saved_state['all_Y'][:, :start_step, :]
            current_states = saved_state['current_states']
            formation_center_x[:start_step] = saved_state['formation_center_x'][:start_step]
            formation_center_y[:start_step] = saved_state['formation_center_y'][:start_step]
            reached_target = saved_state['reached_target']
            target_reached_time = saved_state['target_reached_time']
            all_current_effects[:, :start_step, :] = saved_state['all_current_effects'][:, :start_step, :]
            print(f"从步骤 {start_step} 继续仿真，总步数: {m}")
        except FileNotFoundError:
            print("没有找到之前的仿真状态，从头开始...")
        except Exception as e:
            print(f"加载状态失败: {e}，从头开始...")
    
    print("开始仿真...")
    start_time = time.time()
    
    for i in range(start_step, m):
        t = h * (i + 1)
        T[i] = t
        
        # 当前编队中心位置
        current_center_x = 0
        current_center_y = 0
        
        # 处理每个AUV
        for j in range(num_auvs):
            x = current_states[j]
            
            # 当前坐标（根据累积位置计算当前经纬度）
            lat_now = lat0 + x[6] / 111000
            lon_now = lon0 + x[5] / (111000 * np.cos(lat0 * np.pi / 180))
            depth_now = -x[7]  # 注意 z 是负的
            
            # 当前位置(km)
            current_x_km = x[5] / 1000.0
            current_y_km = x[6] / 1000.0
            
            # 累加用于计算编队中心
            current_center_x += current_x_km
            current_center_y += current_y_km
        
        # 计算编队中心
        current_center_x /= num_auvs
        current_center_y /= num_auvs
        
        # 计算到目标区域中心的距离（用于整体控制逻辑）
        dist_to_target = np.sqrt((current_center_x - target_center_x)**2 + (current_center_y - target_center_y)**2)
        
        # 控制阶段判断和切换 - 使用更保守的平滑过渡
        # 定义控制阶段过渡区间（扩大过渡区间以提高稳定性）
        phase_transition_near = 2.0 * target_radius  # 增加到2.0倍
        phase_transition_far = 4.0 * target_radius   # 增加到4.0倍

        # 计算控制阶段权重 (0: 纯编队保持, 1: 目标跟踪)
        # 使用S形过渡函数实现平滑过渡
        phase_weight = s_curve_transition(dist_to_target, phase_transition_near, phase_transition_far, 0.0, 1.0)

        # 根据权重决定控制阶段（使用更保守的阈值）
        if phase_weight < 0.2 and control_phase == PHASE_TARGET_TRACKING:  # 降低到0.2
            # 从目标跟踪阶段切换到纯编队保持阶段
            control_phase = PHASE_FORMATION_ONLY
            if i % 1000 == 0:  # 减少输出频率
                print(f"\n=== 控制阶段平滑过渡: 进入增强编队保持阶段 ===")
                print(f"距离目标: {dist_to_target:.2f} km, 过渡权重: {phase_weight:.2f}")

            # 在切换时平滑过渡 - 更温和的状态滤波
            for j in range(num_auvs):
                # 计算到目标区域中心的方向向量
                auv_to_target_x = target_center_x - current_states[j][5]/1000.0
                auv_to_target_y = target_center_y - current_states[j][6]/1000.0
                dist = np.sqrt(auv_to_target_x**2 + auv_to_target_y**2)

                if dist > 0:
                    # 根据距离计算理想速度（提升速度限制）
                    ideal_speed = min(1.8, 1.0 + dist * 0.1)  # 最小1.0m/s，最大1.8m/s - 大幅提升

                    # 如果速度过大，平滑减速
                    current_speed = np.sqrt(current_states[j][0]**2 + current_states[j][1]**2)
                    if current_speed > ideal_speed:
                        speed_factor = ideal_speed / current_speed
                        # 更温和的平滑过渡
                        smooth_factor = 0.1  # 进一步降低到0.1，减少速度抑制
                        adjusted_factor = 1.0 - smooth_factor * (1.0 - speed_factor)
                        current_states[j][0] *= adjusted_factor
                        current_states[j][1] *= adjusted_factor

        elif phase_weight > 0.8 and control_phase == PHASE_FORMATION_ONLY:  # 提高到0.8
            # 如果重新远离目标，切回目标跟踪阶段
            control_phase = PHASE_TARGET_TRACKING
            if i % 1000 == 0:  # 减少输出频率
                print(f"\n=== 控制阶段平滑过渡: 返回目标跟踪阶段 ===")
                print(f"距离目标: {dist_to_target:.2f} km, 过渡权重: {phase_weight:.2f}\n")
        
        # 自适应步长调整 - 使用S形过渡函数实现平滑过渡
        step_transition_near = 1.5 * target_radius
        step_transition_far = 4.0 * target_radius
        
        if dist_to_target < step_transition_far:
            # 使用S形过渡函数计算步长
            h_factor = s_curve_transition(dist_to_target, step_transition_near, step_transition_far, 0.0, 1.0)
            h = h_min + (h_default - h_min) * h_factor
            
            if i % 100 == 0:  # 避免过多输出
                print(f"自适应步长: h = {h:.4f} (过渡因子: {h_factor:.2f})")
        else:
            h = h_default
        
        # 动态调整编队形状保持权重
        formation_weight_near = 1.8   # 最大编队保持权重
        formation_weight_far = 1.0    # 最小编队保持权重
        formation_transition_start = 6.0 * target_radius  # 开始增加编队权重的距离
        formation_transition_end = 1.5 * target_radius    # 达到最大编队权重的距离
        
        # 根据距离使用S曲线平滑过渡编队权重
        if dist_to_target < formation_transition_start:
            formation_weight = s_curve_transition(
                dist_to_target, 
                formation_transition_end, 
                formation_transition_start, 
                formation_weight_near, 
                formation_weight_far
            )
        else:
            formation_weight = formation_weight_far
        
        # 到达目标区域临界点增强形状保持
        if dist_to_target < target_radius * 1.2:
            formation_weight *= 1.2  # 额外增强20%
        
        # 计算当前编队形状偏差
        current_formation_deviation = 0
        
        # 准备位置和速度数据用于柔性动力学计算
        positions = []
        velocities = []
        thrust_forces = []
        current_vectors = []
        
        # 处理每个AUV的导航
        for j in range(num_auvs):
            x = current_states[j]
            
            # 当前位置(km)
            current_x_km = x[5] / 1000.0
            current_y_km = x[6] / 1000.0
            
            # 收集位置和速度数据
            positions.append(np.array([x[5], x[6], x[7]]))  # [x, y, z] (单位: m)
            velocities.append(np.array([x[0], x[1], x[2]]))  # [u, v, w] (单位: m/s)
            thrust_forces.append(np.array([thrust_settings[j], 0.0, 0.0]))  # [Fu, 0, 0] (近似)
            current_vectors.append(np.array([all_current_effects[j, i, 0], all_current_effects[j, i, 1], 0.0]))  # [uc, vc, 0]
            
            # 计算相对于编队中心的当前位置
            current_rel_x = current_x_km - current_center_x
            current_rel_y = current_y_km - current_center_y
            
            # 计算与初始编队形状的偏差
            deviation = np.sqrt((current_rel_x - initial_formation[j][0])**2 + 
                                (current_rel_y - initial_formation[j][1])**2)
            current_formation_deviation += deviation
            
            # 使用无波动目标控制函数
            formation_target_x, formation_target_y = calculate_no_oscillation_target(
                current_x_km, current_y_km,
                current_center_x, current_center_y,
                target_positions[j][0], target_positions[j][1],  # 个体目标
                initial_formation[j][0], initial_formation[j][1],
                formation_weight,
                j,  # AUV索引
                target_reached_flags,
                pass_through_mode
            )
            
            # 计算朝向目标的航向 - 考虑编队保持
            target_heading = calculate_target_heading(current_x_km, current_y_km, formation_target_x, formation_target_y)
        
        # 如果使用柔性多体动力学模型，计算虚拟弹簧-阻尼力
        flexible_forces = [np.zeros(3) for _ in range(num_auvs)]
        spring_energies = {}
        damping_powers = {}
        
        if use_flexible_model and formation_graph:
            # 自适应调整参数 - 根据与目标的距离
            dist_factor = min(1.0, dist_to_target / formation_keep_distance)
            
            # 使用S形过渡函数实现平滑过渡（更保守的参数调整）
            # 定义过渡区间（扩大过渡区间）
            dist_near = 2.5 * target_radius  # 增加近距离阈值
            dist_far = 5.0 * target_radius   # 增加远距离阈值

            # 计算刚度和阻尼系数的平滑过渡（更保守的调整范围）
            stiffness_scale = s_curve_transition(dist_to_target, dist_near, dist_far, 0.7, 1.0)  # 最低0.7（更保守）
            damping_scale = s_curve_transition(dist_to_target, dist_near, dist_far, 1.2, 0.95)   # 最高1.2（更保守）

            # 在非常接近目标时，保持较高阻尼以确保稳定
            if dist_to_target < dist_near and i % 2000 == 0:  # 减少输出频率
                print(f"接近目标区域，平滑调整刚度至 {stiffness_scale:.2f}×，阻尼至 {damping_scale:.2f}×")
            
            # 应用刚度和阻尼系数
            adaptive_stiffness = spring_stiffness * stiffness_scale
            adaptive_damping = damping_coefficient * damping_scale
            
            # 检测海流扰动强度，计算自适应参数增强
            current_norms = [np.linalg.norm([all_current_effects[j, i, 0], all_current_effects[j, i, 1]]) 
                            for j in range(num_auvs)]
            avg_current = np.mean(current_norms)
            max_current = np.max(current_norms)
            
            # 在大扰动情况下增强队形刚度，但使用平滑过渡避免突变
            # 在接近目标区域时禁用海流自适应增强
            if max_current > 1.0 and dist_to_target >= 2 * target_radius:  # 强海流条件且不在目标区域附近
                print(f"检测到强海流 ({max_current:.2f} m/s)，适度增强队形刚度")
                # 减小增强因子到1.2（从之前的1.5）
                adaptive_stiffness *= min(1.2, 1.0 + 0.2 * (max_current - 1.0) / 0.5)
            
            # 增加速度共识增益 - 接近目标时增大以确保协同，但也采用平滑过渡
            if dist_to_target < 2 * target_radius:
                # 接近目标区域时，增加共识增益以确保速度一致性
                adaptive_consensus_gain = consensus_gain * 1.5
            else:
                adaptive_consensus_gain = consensus_gain * (1.0 + 0.3 * (1.0 - np.exp(-2.0 * (1.0 - dist_factor))))
            
            flexible_forces, spring_energies, damping_powers = calculate_spring_damper_forces(
                positions, velocities, initial_formation * 1000.0,  # 转换为米
                formation_graph, adaptive_stiffness, adaptive_damping,
                current_vectors=current_vectors
            )
            
            # 计算能量指标
            energy_metrics = calculate_energy_metrics(
                positions, velocities, flexible_forces, spring_energies, damping_powers,
                thrust_forces, current_vectors, mass=auv_mass
            )
            
            # 记录能量数据
            energy_data['time'].append(t)
            energy_data['kinetic_energy'].append(energy_metrics['kinetic_energy'])
            energy_data['potential_energy'].append(energy_metrics['potential_energy'])
            
            # 能量监控机制 - 检测能量异常增长并采取应急措施
            total_energy = energy_metrics['kinetic_energy'] + energy_metrics['potential_energy']
            
            # 设置能量阈值（可根据实际情况调整）
            energy_threshold = 1.8e11  # 降低能量阈值，更早启动紧急措施
            
            if total_energy > energy_threshold:
                print(f"警告: 系统能量过高 ({total_energy:.2e} J)，启动紧急稳定措施!")
                
                # 紧急措施1: 暂时显著降低弹簧刚度（降至25%）
                adaptive_stiffness *= 0.25
                
                # 紧急措施2: 显著增加阻尼（增至3倍）以迅速耗散能量
                adaptive_damping *= 3.0
                
                # 紧急措施3: 减少随机扰动
                for j in range(num_auvs):
                    # 平滑减速而不是突然减速（提升速度阈值）
                    current_speed = np.sqrt(current_states[j][0]**2 + current_states[j][1]**2)
                    if current_speed > 1.2:  # 提升速度阈值从0.5到1.2
                        speed_factor = 0.95  # 减少减速强度，从0.93提升到0.95
                        current_states[j][0] *= speed_factor
                        current_states[j][1] *= speed_factor
                    
                # 记录能量异常
                energy_data['energy_emergency'] = energy_data.get('energy_emergency', []) + [t]
            
            # 累积能量变化
            cumulative_energy['damping_energy'] += energy_metrics['damping_power'] * h
            cumulative_energy['thrust_work'] += energy_metrics['thrust_power'] * h
            cumulative_energy['current_exchange'] += energy_metrics['current_power'] * h
            
            # 记录累积值
            energy_data['damping_energy'].append(cumulative_energy['damping_energy'])
            energy_data['thrust_work'].append(cumulative_energy['thrust_work'])
            energy_data['current_exchange'].append(cumulative_energy['current_exchange'])
            energy_data['total_energy'].append(
                energy_metrics['kinetic_energy'] + 
                energy_metrics['potential_energy'] + 
                cumulative_energy['thrust_work'] + 
                cumulative_energy['current_exchange'] - 
                cumulative_energy['damping_energy']
            )
            
            # 分析编队速度一致性
            velocities_array = np.array(velocities)
            speeds = np.linalg.norm(velocities_array, axis=1)
            speed_std = np.std(speeds)
            speed_mean = np.mean(speeds)
            speed_cv = speed_std / speed_mean if speed_mean > 0 else 0  # 速度变异系数
            
            if i % 500 == 0:
                print(f"编队速度分析: 平均={speed_mean:.2f} m/s, 标准差={speed_std:.2f}, 变异系数={speed_cv:.2f}")
                print(f"弹簧势能: {energy_metrics['potential_energy']:.2f} J, 阻尼功率: {energy_metrics['damping_power']:.2f} W")
        
        # 处理每个AUV的动力学
        for j in range(num_auvs):
            x = current_states[j]
            
            # 当前位置(km)
            current_x_km = x[5] / 1000.0
            current_y_km = x[6] / 1000.0
            
            # 计算相对于编队中心的当前位置
            current_rel_x = current_x_km - current_center_x
            current_rel_y = current_y_km - current_center_y
            
            # 计算与初始编队形状的偏差
            deviation = np.sqrt((current_rel_x - initial_formation[j][0])**2 + 
                                (current_rel_y - initial_formation[j][1])**2)
            current_formation_deviation += deviation
            
            # 使用无波动目标控制函数
            formation_target_x, formation_target_y = calculate_no_oscillation_target(
                current_x_km, current_y_km,
                current_center_x, current_center_y,
                target_positions[j][0], target_positions[j][1],  # 个体目标
                initial_formation[j][0], initial_formation[j][1],
                formation_weight,
                j,  # AUV索引
                target_reached_flags,
                pass_through_mode
            )
            
            # 计算朝向目标的航向 - 考虑编队保持
            target_heading = calculate_target_heading(current_x_km, current_y_km, formation_target_x, formation_target_y)
            
            # 检查是否接近边界，调整目标航向远离边界（减少边界影响以获得更直线路径）
            boundary_margin = 3.0  # 减少边界安全距离（从5.0减少到3.0千米）
            boundary_influence = False

            # 创建边界避让航向
            boundary_heading = target_heading  # 默认与目标航向相同

            # 检查各个边界的距离，如果接近边界则调整航向
            if current_x_km < (x_boundary_min + boundary_margin):
                # 接近左边界，向右避开
                boundary_heading = 0.0  # 正东方向
                boundary_influence = True
            elif current_x_km > (x_boundary_max - boundary_margin):
                # 接近右边界，向左避开
                boundary_heading = np.pi  # 正西方向
                boundary_influence = True

            if current_y_km < (y_boundary_min + boundary_margin):
                # 接近下边界，向上避开
                boundary_heading = np.pi/2  # 正北方向
                boundary_influence = True
            elif current_y_km > (y_boundary_max - boundary_margin):
                # 接近上边界，向下避开
                boundary_heading = 3*np.pi/2  # 正南方向
                boundary_influence = True

            # 综合考虑目标航向和边界约束，减少边界影响以获得更直线路径
            if boundary_influence:
                # 修改边界处理逻辑 - 大幅减少边界影响
                if dist_to_target < 3 * target_radius:  # 扩大目标优先区域
                    # 靠近目标区域时，大幅减弱边界影响
                    boundary_weight = 0.15  # 从0.3减少到0.15
                    target_weight = 1.0 - boundary_weight

                    # 将边界航向和目标航向混合
                    x_component = target_weight * np.cos(target_heading) + boundary_weight * np.cos(boundary_heading)
                    y_component = target_weight * np.sin(target_heading) + boundary_weight * np.sin(boundary_heading)
                    target_heading = np.arctan2(y_component, x_component)
                else:
                    # 远离目标时，边界影响也要减弱
                    boundary_weight = 0.5  # 从完全替换改为50%权重
                    target_weight = 1.0 - boundary_weight

                    x_component = target_weight * np.cos(target_heading) + boundary_weight * np.cos(boundary_heading)
                    y_component = target_weight * np.sin(target_heading) + boundary_weight * np.sin(boundary_heading)
                    target_heading = np.arctan2(y_component, x_component)
            
            # 边界检查：限制在数据有效范围内
            lat_margin = 0.05  # 边距
            lon_margin = 0.05
            depth_margin = 5.0
            
            # 检查是否接近边界
            is_inside = (
                depth.min() + depth_margin <= depth_now <= depth.max() - depth_margin and
                lat.min() + lat_margin <= lat_now <= lat.max() - lat_margin and
                lon.min() + lon_margin <= lon_now <= lon.max() - lon_margin
            )
            
            if not is_inside:
                # 限制在数据有效范围内
                lat_now = np.clip(lat_now, lat.min() + lat_margin, lat.max() - lat_margin)
                lon_now = np.clip(lon_now, lon.min() + lon_margin, lon.max() - lon_margin)
                depth_now = np.clip(depth_now, depth.min() + depth_margin, depth.max() - depth_margin)

            # 获取当前位置插值的海流速度
            try:
                u_c, v_c = ocean.get_current(depth_now, lat_now, lon_now)
                
                # 检查返回的海流值是否有效
                if np.isnan(u_c) or np.isnan(v_c):
                    # 尝试周围点的平均值
                    u_c_vals = []
                    v_c_vals = []
                    for d_offset in [-1, 0, 1]:
                        for lat_offset in [-0.01, 0, 0.01]:
                            for lon_offset in [-0.01, 0, 0.01]:
                                try:
                                    d = depth_now + d_offset
                                    la = lat_now + lat_offset
                                    lo = lon_now + lon_offset
                                    if (depth.min() + depth_margin <= d <= depth.max() - depth_margin and
                                        lat.min() + lat_margin <= la <= lat.max() - lat_margin and
                                        lon.min() + lon_margin <= lo <= lon.max() - lon_margin):
                                        temp_u, temp_v = ocean.get_current(d, la, lo)
                                        if not (np.isnan(temp_u) or np.isnan(temp_v)):
                                            u_c_vals.append(temp_u)
                                            v_c_vals.append(temp_v)
                                except:
                                    continue
                    
                    if u_c_vals and v_c_vals:
                        u_c = np.mean(u_c_vals)
                        v_c = np.mean(v_c_vals)
                    else:
                        u_c, v_c = 0.0, 0.0
                
                # 确保值是浮点数
                u_c = float(u_c)
                v_c = float(v_c)
                
                # 移除海流放大
                # u_c *= 10  # 删除放大因子
                # v_c *= 10  # 删除放大因子
                
                w_c = 0.0
            except Exception as e:
                print(f"海流获取错误: {e}")
                u_c, v_c, w_c = 0.0, 0.0, 0.0
            
            # 记录海流影响
            all_current_effects[j, i, 0] = u_c
            all_current_effects[j, i, 1] = v_c
            all_current_effects[j, i, 2] = np.sqrt(u_c**2 + v_c**2)
            
            # 海流三维向量
            current_vector = [u_c, v_c, w_c]
            
            # 计算海流方向和强度
            current_strength = np.sqrt(u_c**2 + v_c**2)
            # 限制海流影响最大值，避免过大海流导致系统不稳定
            current_strength = min(current_strength, 1.0)
            if current_strength > 0:
                current_direction = np.arctan2(v_c, u_c)
            else:
                current_direction = 0.0
                
            # 计算海流方向与AUV当前航向的夹角
            heading_current_diff = AdjustAngle(current_direction - x[9])
            
            # 计算与目标航向的差值
            target_heading_diff = AdjustAngle(target_heading - x[9])
            
            # 控制输入 - 每个AUV使用不同参数
            # 如果AUV已到达目标，完全停止推力以停留在原地
            if target_reached_flags[j]:
                Fu = 0.0  # 完全停止推力，避免任何移动
            else:
                Fu = thrust_settings[j]  # 恒定推力，每个AUV不同
            
            # 添加舵角扰动 - 减少扰动以获得更直线的路径
            # 1. 海流横向分量产生的扰动（减少影响）
            cross_current_effect = np.sin(heading_current_diff) * current_strength * 0.08  # 从0.15减少到0.08

            # 2. 随机扰动生成（大幅减少）
            if i % 3 == 0:
                base_random_effect = 0.02 * (np.random.randn()) * (1.0 + 0.1 * j)  # 从0.06减少到0.02
            else:
                base_random_effect = 0.008 * (np.random.randn())  # 从0.02减少到0.008

            # 随机扰动将在后面根据控制阶段进行缩放，这里只计算基础值
            random_effect = base_random_effect
            
            # 3. 添加朝向目标的航向修正
            # 计算到目标的距离 - 使用正确的目标位置
            dist_to_target = np.sqrt((current_x_km - formation_target_x)**2 + (current_y_km - formation_target_y)**2)
            
            # 根据当前控制阶段调整航向修正
            if control_phase == PHASE_FORMATION_ONLY:
                # 纯编队保持阶段：保留小比例航向修正而不是完全关闭
                heading_weight = 0.15  # 保留15%的航向修正力
                heading_correction = heading_weight * np.sin(target_heading_diff)
            else:
                # 目标跟踪阶段：根据到目标距离平滑调整航向修正
                # 使用平滑的sigmoid函数进行权重过渡
                transition_pos = 3 * target_radius  # 开始过渡的位置
                transition_factor = 1.0 / (1.0 + np.exp(-0.5 * (dist_to_target - transition_pos)))  # sigmoid过渡
                heading_weight = 0.15 + 0.85 * transition_factor  # 从0.15到1.0平滑过渡
                heading_correction = 0.3 * np.sin(target_heading_diff) * heading_weight
            
            # 调整随机扰动系数 - 更保守的扰动控制
            # 使用S形过渡函数统一处理随机扰动（更大的过渡区间）
            disturbance_scale = s_curve_transition(dist_to_target, phase_transition_near, phase_transition_far, 0.1, 1.0)  # 最小值降到0.1

            # 在目标区域附近进一步减少扰动
            if dist_to_target < target_radius * 2.0:
                disturbance_scale *= 0.5  # 目标区域附近扰动减半

            # 合并所有舵角扰动，使用平滑过渡的扰动系数
            deltav = deltav_settings[j]

            # 如果AUV已到达目标，完全停止舵角调整以保持稳定
            if target_reached_flags[j]:
                # 已到达目标的AUV：完全停止所有扰动和修正
                deltav = deltav_settings[j]  # 只保持基础舵角，不添加任何扰动
            else:
                # 应用扰动和修正，根据控制阶段调整
                if control_phase == PHASE_FORMATION_ONLY:
                    # 纯编队保持阶段，应用更少的扰动
                    deltav += cross_current_effect * disturbance_scale * 0.2  # 进一步减小到0.2
                    deltav += random_effect * disturbance_scale * 0.1  # 进一步减小到0.1
                    deltav += heading_correction
                else:
                    # 目标跟踪阶段，正常应用扰动
                    deltav += cross_current_effect * disturbance_scale * 0.8  # 稍微减小
                    deltav += random_effect * disturbance_scale * 0.6  # 稍微减小
                    deltav += heading_correction
            
            deltah = deltah_settings[j]
            
            # 获取柔性力（如果使用柔性模型且AUV未到达目标）
            if use_flexible_model and not target_reached_flags[j]:
                flex_force = flexible_forces[j]
            else:
                flex_force = np.zeros(3)  # 到达目标的AUV不受柔性力影响
            
            # 龙格-库塔积分 - 修改为包含柔性力，到达目标的AUV不受海流影响
            if target_reached_flags[j]:
                # 到达目标的AUV不受海流影响
                zero_current = np.array([0.0, 0.0, 0.0])
                k1 = Infante_3d_with_current(t, x, [Fu, deltav, deltah], zero_current, flex_force=flex_force)
                k2 = Infante_3d_with_current(t + h / 2, x + h / 2 * k1, [Fu, deltav, deltah], zero_current, flex_force=flex_force)
                k3 = Infante_3d_with_current(t + h / 2, x + h / 2 * k2, [Fu, deltav, deltah], zero_current, flex_force=flex_force)
                k4 = Infante_3d_with_current(t + h, x + h * k3, [Fu, deltav, deltah], zero_current, flex_force=flex_force)
            else:
                # 未到达目标的AUV正常受海流影响
                k1 = Infante_3d_with_current(t, x, [Fu, deltav, deltah], current_vector, flex_force=flex_force)
                k2 = Infante_3d_with_current(t + h / 2, x + h / 2 * k1, [Fu, deltav, deltah], current_vector, flex_force=flex_force)
                k3 = Infante_3d_with_current(t + h / 2, x + h / 2 * k2, [Fu, deltav, deltah], current_vector, flex_force=flex_force)
                k4 = Infante_3d_with_current(t + h, x + h * k3, [Fu, deltav, deltah], current_vector, flex_force=flex_force)

            x = x + h / 6 * (k1 + 2 * k2 + 2 * k3 + k4)

            # 完全移除目标区域稳定控制 - 避免任何波动
            # 无波动控制：不对AUV状态进行任何额外修改
            # 让AUV自然地按照目标航向前进，到达目标后自然穿越或停止

            # 状态限制 - 确保AUV不超出边界
            # 转换为千米检查边界
            x_km = x[5] / 1000.0
            y_km = x[6] / 1000.0
            
            # 距离边界的缓冲区（千米）
            buffer = 1.0
            
            # 检查是否接近边界
            if x_km <= (x_boundary_min + buffer):
                # 接近左边界，施加向右的力
                x[0] += 0.3  # 增加向右的速度
                x[5] = (x_boundary_min + buffer) * 1000  # 设置最小x位置
            elif x_km >= (x_boundary_max - buffer):
                # 接近右边界，施加向左的力
                x[0] -= 0.3  # 增加向左的速度
                x[5] = (x_boundary_max - buffer) * 1000  # 设置最大x位置
            
            if y_km <= (y_boundary_min + buffer):
                # 接近下边界，施加向上的力
                x[1] += 0.3  # 增加向上的速度
                x[6] = (y_boundary_min + buffer) * 1000  # 设置最小y位置
            elif y_km >= (y_boundary_max - buffer):
                # 接近上边界，施加向下的力
                x[1] -= 0.3  # 增加向下的速度
                x[6] = (y_boundary_max - buffer) * 1000  # 设置最大y位置
            
            # 最终位置限制
            x[5] = LimitMaxMin(x[5], (x_boundary_max - buffer/2) * 1000, (x_boundary_min + buffer/2) * 1000)  # x方向
            x[6] = LimitMaxMin(x[6], (y_boundary_max - buffer/2) * 1000, (y_boundary_min + buffer/2) * 1000)  # y方向
            x[7] = LimitMaxMin(x[7], 0, -0.5)  # z方向限制在水面以下0.5米
            
            # 更强随机速度扰动 (减弱10倍，并移除周期性波动)
            # 基础随机扰动
            base_disturbance = np.random.randn()
            
            # 海流影响因子：减少海流扰动以获得更直线路径
            current_factor = 0.02 * current_strength  # 从0.05减少到0.02

            # AUV编号影响：减少个体差异扰动
            auv_factor = 1.0 + 0.1 * np.sin(j * np.pi/5)  # 从0.25减少到0.1

            # 使用相同的S形过渡函数控制扰动强度，但减少扰动幅度
            velocity_disturbance_scale = s_curve_transition(dist_to_target, phase_transition_near, phase_transition_far, 0.1, 0.5)  # 从(0.2,1.0)减少到(0.1,0.5)

            # 综合随机扰动计算 - 大幅减少扰动以获得更直线路径
            u_disturbance = 0.03 * base_disturbance * auv_factor * velocity_disturbance_scale  # 从0.08减少到0.03
            u_disturbance += current_factor * velocity_disturbance_scale * 0.8  # 从1.5减少到0.8
            u_disturbance += 0.02 * np.random.randn() * velocity_disturbance_scale  # 从0.06减少到0.02

            v_disturbance = 0.02 * np.random.randn() * auv_factor * velocity_disturbance_scale  # 从0.05减少到0.02
            v_disturbance += 0.015 * current_factor * velocity_disturbance_scale  # 从0.035减少到0.015

            w_disturbance = 0.01 * np.random.randn() * auv_factor * velocity_disturbance_scale  # 从0.025减少到0.01
            
            # 应用扰动
            x[0] += u_disturbance
            x[1] += v_disturbance
            x[2] += w_disturbance
            
            # 计算当前合速度
            current_speed = np.sqrt(x[0]**2 + x[1]**2 + x[2]**2)
            
            # 改进的速度限制：使用平滑缩放而不是硬限幅
            if current_speed > max_speed:
                speed_scale = max_speed / current_speed
                x[0] *= speed_scale
                x[1] *= speed_scale
                x[2] *= speed_scale
            
            # 如果AUV已到达目标，强制停止所有运动
            if target_reached_flags[j]:
                # 已到达目标的AUV：完全停止运动
                x[0] = 0.0  # 前进速度设为0
                x[1] = 0.0  # 横向速度设为0
                x[2] = 0.0  # 垂直速度设为0
            else:
                # 保持一个最小速度（提升最小速度要求）
                min_speed = 0.8  # 从0.3提升到0.8 m/s
                if current_speed < min_speed and x[0] < min_speed:
                    # 平滑增加前进速度，而不是硬限幅
                    speed_boost = (min_speed - current_speed) * 0.8  # 增加boost系数
                    if speed_boost > 0:
                        x[0] += speed_boost
            
            # 记录最终合速度
            final_speed = np.sqrt(x[0]**2 + x[1]**2 + x[2]**2)
            
            # 姿态角速率限制
            x[3] = LimitMaxMin(x[3], 0.2, -0.2)  # q 
            x[4] = LimitMaxMin(x[4], 0.2, -0.2)  # r
            
            # 姿态角限制
            x[8] = LimitMaxMin(x[8], 15 * D2R, -15 * D2R)  # theta 限制在±15度
            
            # 海流对航向的影响
            # 1. 海流产生的横向力矩
            yaw_moment_current = np.sin(heading_current_diff) * current_strength * 0.15
            
            # 2. 添加随机湍流影响（根据控制阶段调整）
            if i % 2 == 0:
                base_yaw_turbulence = 0.04 * np.random.randn() * (1.0 + 0.25 * j)
            else:
                base_yaw_turbulence = 0.015 * np.random.randn()
                
            # 使用相同的S形过渡函数控制扰动强度
            yaw_disturbance_scale = s_curve_transition(dist_to_target, phase_transition_near, phase_transition_far, 0.2, 1.0)
            yaw_turbulence = base_yaw_turbulence * yaw_disturbance_scale
            
            # 3. 添加朝向目标的修正
            # 使用相同的航向权重
            yaw_heading_correction = heading_correction * 0.5  # 减少对偏航的影响
            
            # 合并所有航向影响
            yaw_rate = yaw_moment_current * yaw_disturbance_scale + yaw_turbulence + yaw_heading_correction
            x[9] = AdjustAngle(x[9] + yaw_rate)
            
            # 更新当前状态
            current_states[j] = x
            # 记录状态
            all_Y[j, i] = x
        
        # 计算当前编队中心位置
        formation_center_x[i] = current_center_x
        formation_center_y[i] = current_center_y
        
        # 计算当前编队形状
        for j in range(num_auvs):
            current_x_km = all_Y[j, i, 5] / 1000.0
            current_y_km = all_Y[j, i, 6] / 1000.0
            final_formation[j] = [current_x_km - current_center_x, current_y_km - current_center_y]
        
        # 检查是否所有AUV都到达各自目标点（使用目标到达标志）
        reached_count = sum(target_reached_flags)
        all_reached = reached_count == num_auvs

        # 放宽形状保持要求，优先考虑目标到达
        formation_ok = True
        if all_reached:
            # 计算平均形状偏差
            formation_deviation = current_formation_deviation / num_auvs
            # 放宽形状偏差要求，从15%增加到30%
            if formation_deviation > target_radius * 0.3:  # 从0.15放宽到0.3
                formation_ok = False
                if i % 1000 == 0:
                    print(f"所有AUV已达到目标位置，但编队形状偏差过大: {formation_deviation:.2f} km")

        # 修改判断逻辑：只要所有AUV到达目标点就算成功，不强制要求完美的编队形状
        if all_reached and not reached_target:
            reached_target = True
            target_reached_time = t
            print(f"\n所有{num_auvs}个AUV在时间 {t:.1f} 秒都到达了各自的目标点!")
            print(f"编队中心距离目标区域中心: {dist_to_target:.2f}km")
            if not formation_ok:
                print(f"注意：编队形状有轻微偏差，但任务目标已完成")

            # 如果启用自动停止，则终止仿真
            if auto_stop:
                print(f"自动停止已启用，仿真将在所有AUV达到目标后终止。")
                # 记录实际的仿真步数
                actual_steps = i + 1
                print(f"总仿真步数: {actual_steps}/{m} ({actual_steps/m*100:.2f}%)")
                break
            else:
                print("所有AUV已到达各自目标点，但仿真继续进行...")
        elif i % 5000 == 0 and reached_count > 0:  # 每5000步报告一次进度
            print(f"进度: {reached_count}/{num_auvs} 个AUV已到达目标点")
        
        # 优化数据记录 - 只在特定步数记录数据，减少内存使用和计算量
        if i % data_record_interval == 0:
            # 保存数据点
            all_data['time'].append(t)
            all_data['formation_center_x'].append(current_center_x)
            all_data['formation_center_y'].append(current_center_y)
            all_data['dist_to_target'].append(dist_to_target)
            all_data['formation_deviation'].append(current_formation_deviation / num_auvs)  # 平均偏差
            
            # 减少能量数据记录频率
            if use_flexible_model:
                energy_data['time'].append(t)
                energy_data['kinetic_energy'].append(energy_metrics['kinetic_energy'])
                energy_data['potential_energy'].append(energy_metrics['potential_energy'])
                
                # 记录累积值
                energy_data['damping_energy'].append(cumulative_energy['damping_energy'])
                energy_data['thrust_work'].append(cumulative_energy['thrust_work'])
                energy_data['current_exchange'].append(cumulative_energy['current_exchange'])
                energy_data['total_energy'].append(
                    energy_metrics['kinetic_energy'] + 
                    energy_metrics['potential_energy'] + 
                    cumulative_energy['thrust_work'] + 
                    cumulative_energy['current_exchange'] - 
                    cumulative_energy['damping_energy']
                )
        
        # 减少进度输出频率以提高计算速度
        if i % progress_output_interval == 0:  # 使用计算得到的进度输出间隔
            elapsed = time.time() - start_time
            percent_done = i/m*100
            print(f"仿真进度: {i}/{m} 步 ({percent_done:.1f}%) - 已用时间: {elapsed:.1f}秒 - 速度: {i/max(elapsed,1):.1f}步/秒", end="\r")
            if i > 0 and i % detail_output_interval == 0:  # 使用计算得到的详细输出间隔
                print(f"\n编队中心位置: ({formation_center_x[i]:.1f}, {formation_center_y[i]:.1f}) km, 距目标: {dist_to_target:.1f} km")
    
    # 确保我们有一个有效的actual_steps变量
    if 'actual_steps' not in locals():
        actual_steps = m
    
    print(f"\n仿真结束! 总用时: {time.time() - start_time:.1f}秒, 实际步数: {actual_steps}/{m}")
    
    if save_results:
        # 创建用于数据采样的时间索引
        time_indices = list(range(0, actual_steps, data_record_interval))
        # 确保至少包含第一个和最后一个时间点
        if 0 not in time_indices:
            time_indices.insert(0, 0)
        if actual_steps - 1 not in time_indices:
            time_indices.append(actual_steps - 1)
            
        # 创建保存数据的DataFrame
        trajectory_data = pd.DataFrame({
            'time': [T[i] for i in time_indices],
        })
        for j in range(num_auvs):
            # 添加每个AUV的位置数据
            trajectory_data[f'auv{j+1}_x'] = [all_Y[j, time_indices[i], 5]/1000 for i in range(len(time_indices))]
            trajectory_data[f'auv{j+1}_y'] = [all_Y[j, time_indices[i], 6]/1000 for i in range(len(time_indices))]
            trajectory_data[f'auv{j+1}_u'] = [all_Y[j, time_indices[i], 0] for i in range(len(time_indices))]
            trajectory_data[f'auv{j+1}_v'] = [all_Y[j, time_indices[i], 1] for i in range(len(time_indices))]
            trajectory_data[f'auv{j+1}_speed'] = [np.sqrt(all_Y[j, time_indices[i], 0]**2 + all_Y[j, time_indices[i], 1]**2) 
                                                for i in range(len(time_indices))]
            trajectory_data[f'auv{j+1}_heading'] = [all_Y[j, time_indices[i], 9] * R2D for i in range(len(time_indices))]
            # 添加海流数据
            trajectory_data[f'current{j+1}_u'] = [all_current_effects[j, time_indices[i], 0] for i in range(len(time_indices))]
            trajectory_data[f'current{j+1}_v'] = [all_current_effects[j, time_indices[i], 1] for i in range(len(time_indices))]
            trajectory_data[f'current{j+1}_strength'] = [all_current_effects[j, time_indices[i], 2] for i in range(len(time_indices))]
        
        # 保存轨迹数据
        trajectory_file = os.path.join(output_dir, 'trajectory_data.csv')
        trajectory_data.to_csv(trajectory_file, index=False)
        print(f"轨迹数据已保存到: {trajectory_file}")
        
        # 创建并保存编队数据
        formation_data = pd.DataFrame({
            'time': all_data['time'],
            'formation_center_x': all_data['formation_center_x'],
            'formation_center_y': all_data['formation_center_y'],
            'dist_to_target': all_data['dist_to_target'],
            'formation_deviation': all_data['formation_deviation']
        })
        formation_file = os.path.join(output_dir, 'formation_data.csv')
        formation_data.to_csv(formation_file, index=False)
        print(f"编队数据已保存到: {formation_file}")
        
        # 保存仿真摘要信息
        summary_file = os.path.join(output_dir, 'simulation_summary.txt')
        with open(summary_file, 'w') as f:
            f.write(f"仿真摘要\n")
            f.write(f"==========\n")
            f.write(f"仿真步数: {actual_steps}/{m}\n")
            f.write(f"仿真时间: {time.time() - start_time:.2f}秒\n")
            f.write(f"目标位置: ({target_center_x}, {target_center_y}) km\n")
            f.write(f"起始位置: ({center_x}, {center_y}) km\n")
            f.write(f"实际距离: {np.sqrt((center_x - target_center_x)**2 + (center_y - target_center_y)**2):.2f} km\n")
            if reached_target:
                f.write(f"到达目标时间: {target_reached_time:.2f}秒\n")
            else:
                f.write("未到达目标\n")
            f.write(f"柔性模型: {'启用' if use_flexible_model else '禁用'}\n")
            if use_flexible_model:
                f.write(f"弹簧刚度: {spring_stiffness}\n")
                f.write(f"阻尼系数: {damping_coefficient}\n")
                f.write(f"编队拓扑: {formation_topology}\n")
        print(f"仿真摘要已保存到: {summary_file}")
        
        # 如果跳过绘图则提前返回
        if skip_plots:
            return
            
        # 开始生成图表...
        print("开始生成图表...")
        # 将AUV轨迹转换为相对坐标（千米）
        all_tracks_x_km = []
        all_tracks_y_km = []
        for j in range(num_auvs):
            track_x_km = all_Y[j, :, 5] / 1000.0
            track_y_km = all_Y[j, :, 6] / 1000.0
            all_tracks_x_km.append(track_x_km)
            all_tracks_y_km.append(track_y_km)
        
        # 创建并保存轨迹数据文件 - 修复数组长度不匹配问题
        trajectory_rows = []
        for i in range(len(T)):
            for j in range(num_auvs):
                trajectory_rows.append({
                    'time': T[i],
                    'auv_id': j+1,
                    'x_km': all_tracks_x_km[j][i],
                    'y_km': all_tracks_y_km[j][i],
                    'u': all_Y[j, i, 0],
                    'v': all_Y[j, i, 1],
                    'w': all_Y[j, i, 2],
                    'heading': all_Y[j, i, 9] * R2D,
                    'current_u': all_current_effects[j, i, 0],
                    'current_v': all_current_effects[j, i, 1],
                    'current_strength': all_current_effects[j, i, 2]
                })
        
        trajectory_df = pd.DataFrame(trajectory_rows)
        trajectory_csv_path = os.path.join(output_dir, 'trajectory_data.csv')
        trajectory_df.to_csv(trajectory_csv_path, index=False)
        print(f"轨迹数据已保存到: {trajectory_csv_path}")
        
        # 创建并保存编队数据文件
        formation_df = pd.DataFrame(all_data)
        formation_csv_path = os.path.join(output_dir, 'formation_data.csv')
        formation_df.to_csv(formation_csv_path, index=False)
        print(f"编队数据已保存到: {formation_csv_path}")
        
        # 绘制AUV轨迹图 - 与circle.py格式一致
        fig, ax = plt.subplots(figsize=(8, 8))
        
        # 使用实际海流数据绘制背景
        speed = np.clip(np.sqrt(uo_plot**2 + vo_plot**2), 0.06, 0.11)
        
        contour = ax.contourf(
            dx_km, dy_km, speed,
            cmap='RdBu_r',     # 颜色图选择
            levels=50,          # 等高线数
            vmin=0.06,          # 手动设置最小值
            vmax=0.11           # 手动设置最大值
        )
        
        # 添加颜色条 - 精确匹配circle.py的设置
        tick_min = 0.06
        tick_max = 0.11
        tick_num = 11  # 11个刻度
        cbar = fig.colorbar(contour, ax=ax, ticks=np.linspace(tick_min, tick_max, tick_num))
        cbar.set_label('Current Speed [m/s]', fontsize=16)
        cbar.ax.tick_params(labelsize=14)
        
        # 绘制海流箭头
        factor = 2
        uo_dense = zoom(uo_plot, factor)
        vo_dense = zoom(vo_plot, factor)
        x_dense = zoom(dx_km, factor)
        y_dense = zoom(dy_km, factor)
        
        # 画更密的箭头
        ax.quiver(x_dense, y_dense, uo_dense, vo_dense, scale=3e0, color='k', width=0.002)
        
        # 绘制目标区域和个体目标点
        target_circle = plt.Circle((target_center_x, target_center_y), target_radius, color='g', fill=False, linestyle='--', linewidth=2)
        ax.add_patch(target_circle)
        ax.plot(target_center_x, target_center_y, 'g*', markersize=15)  # 目标区域中心标记
        ax.text(target_center_x + 2, target_center_y + 2, '目标区域', fontsize=12, color='g')

        # 绘制每个AUV的个体目标点
        for idx, target_pos in enumerate(target_positions):
            ax.plot(target_pos[0], target_pos[1], 'r+', markersize=8, markeredgewidth=2)  # 个体目标标记
            ax.text(target_pos[0] + 0.5, target_pos[1] + 0.5, f'T{idx+1}', fontsize=8, color='r')
        
        # 绘制AUV初始圆形排列
        initial_x_km = []
        initial_y_km = []
        
        for j in range(num_auvs):
            # 使用第一个点作为初始位置
            initial_x_km.append(all_tracks_x_km[j][0])
            initial_y_km.append(all_tracks_y_km[j][0])
        
        # 绘制初始圆形排列
        initial_x_km_closed = np.append(initial_x_km, initial_x_km[0])
        initial_y_km_closed = np.append(initial_y_km, initial_y_km[0])
        
        ax.plot(initial_x_km_closed, initial_y_km_closed, color='red', linewidth=1.5, linestyle='-')  # 连线
        ax.plot(initial_x_km, initial_y_km, marker='^', color='red', markersize=8, linestyle='None')  # 三角形标记
        
        # 添加初始编队标签
        ax.plot([], [], color='red', linewidth=1.5, linestyle='-', label='Initial Formation')
        
        # 绘制AUV轨迹 - 使用较细的线条，不绘制航向箭头
        for j in range(num_auvs):
            # 绘制完整轨迹
            ax.plot(all_tracks_x_km[j], all_tracks_y_km[j], '-', color=colors[j], linewidth=1.0, label=f'AUV{j+1}')
            
            # 绘制起点和终点
            ax.plot(all_tracks_x_km[j][0], all_tracks_y_km[j][0], 'o', color=colors[j], markersize=6)
            ax.plot(all_tracks_x_km[j][-1], all_tracks_y_km[j][-1], 's', color=colors[j], markersize=6)
        
        # 绘制最终编队形状
        final_x_km = []
        final_y_km = []
        for j in range(num_auvs):
            final_x_km.append(all_tracks_x_km[j][-1])
            final_y_km.append(all_tracks_y_km[j][-1])
        
        # 闭合最终编队形状
        final_x_km_closed = np.append(final_x_km, final_x_km[0])
        final_y_km_closed = np.append(final_y_km, final_y_km[0])
        
        # 绘制最终编队形状（红色虚线）
        ax.plot(final_x_km_closed, final_y_km_closed, 'r--', linewidth=1.5, label='Final Formation')
        
        # 绘制编队中心轨迹
        ax.plot(formation_center_x, formation_center_y, 'k--', linewidth=1.5, label='Formation Center')
        
        # 标签 & 样式 - 精确匹配circle.py
        ax.set_xlabel("X [km]", fontsize=15)
        ax.set_ylabel("Y [km]", fontsize=15)
        ax.set_title("AUVs Moving to Target Area with Ocean Current", fontsize=20)
        
        # 精确设置坐标轴原点
        ax.spines['left'].set_position(('data', 0))
        ax.spines['bottom'].set_position(('data', 0))
        ax.spines['right'].set_color('none')
        ax.spines['top'].set_color('none')
        
        # 设置刻度位置
        ax.xaxis.set_ticks_position('bottom')
        ax.yaxis.set_ticks_position('left')
        ax.set_xticks(np.arange(0, dx_km.max(), 20))
        ax.set_yticks(np.arange(0, dy_km.max(), 20))
        
        ax.set_xlim(0, dx_km.max())
        ax.set_ylim(0, dy_km.max())
        ax.set_aspect('equal')
        ax.grid(False)
        
        # 添加图例，调整位置和大小
        ax.legend(loc='lower left', fontsize=10, framealpha=0.7)
        
        # 紧凑布局，去除白边
        fig.subplots_adjust(left=0.12, right=0.92, top=0.92, bottom=0.12)
        
        # 打印坐标范围
        print("X范围: 0 ~", dx_km.max(), "km")
        print("Y范围: 0 ~", dy_km.max(), "km")
        
        # 保存轨迹图
        trajectory_plot_path = os.path.join(output_dir, 'trajectory_plot.png')
        plt.savefig(trajectory_plot_path, dpi=300, bbox_inches='tight')
        print(f"轨迹图已保存到: {trajectory_plot_path}")
        
        # 如果到达目标，显示到达时间
        if reached_target:
            print(f"编队成功到达目标区域，用时 {target_reached_time:.1f} 秒")
        else:
            # 检查是否有部分AUV到达目标
            reached_count = sum(target_reached_flags)
            if reached_count > 0:
                print(f"仿真结束，部分AUV已到达目标区域 ({reached_count}/{num_auvs})")
            else:
                print("仿真结束，编队未能到达目标区域")
        
        # 计算编队形状保持指标
        initial_distances = []
        final_distances = []
        
        # 计算初始和最终相邻AUV之间的距离
        for j in range(num_auvs):
            next_j = (j + 1) % num_auvs
            initial_dist = np.sqrt((initial_x_km[j] - initial_x_km[next_j])**2 + 
                                  (initial_y_km[j] - initial_y_km[next_j])**2)
            final_dist = np.sqrt((final_x_km[j] - final_x_km[next_j])**2 + 
                                (final_y_km[j] - final_y_km[next_j])**2)
            
            initial_distances.append(initial_dist)
            final_distances.append(final_dist)
        
        # 计算距离变化率
        distance_changes = [abs(final_distances[i] - initial_distances[i]) / initial_distances[i] * 100 
                            for i in range(num_auvs)]
        avg_distance_change = np.mean(distance_changes)
        
        print(f"\n编队形状保持分析:")
        print(f"初始平均相邻距离: {np.mean(initial_distances):.2f} km")
        print(f"最终平均相邻距离: {np.mean(final_distances):.2f} km")
        print(f"平均距离变化率: {avg_distance_change:.2f}%")
        
        if avg_distance_change < 20:
            print("结论: 编队形状保持良好 (变化率<20%)")
        elif avg_distance_change < 50:
            print("结论: 编队形状部分保持 (变化率<50%)")
        else:
            print("结论: 编队形状变化显著 (变化率>=50%)")
        
        # 如果使用了柔性多体动力学模型，绘制编队拓扑结构
        if use_flexible_model and formation_graph:
            plt.figure(figsize=(8, 8))
            
            # 获取最终编队位置（最后一个时间步）
            final_positions = []
            for j in range(num_auvs):
                final_x = all_Y[j, -1, 5] / 1000.0  # 转换为千米
                final_y = all_Y[j, -1, 6] / 1000.0
                final_positions.append((final_x, final_y))
            
            # 计算节点位置
            node_pos = {j: final_positions[j] for j in range(num_auvs)}
            
            # 计算连接边的弹簧势能
            edge_widths = []
            for i, j in formation_graph.edges():
                # 计算相对位置向量
                ri = final_positions[i]
                rj = final_positions[j]
                r_ij = np.array(ri) - np.array(rj)
                r_norm = np.linalg.norm(r_ij)
                
                # 计算期望距离
                initial_pos_i = initial_formation[i]
                initial_pos_j = initial_formation[j]
                desired_dist = np.linalg.norm(initial_pos_i - initial_pos_j)
                
                # 弹簧势能 (用于边的宽度)
                spring_energy = 0.5 * spring_stiffness * (r_norm - desired_dist)**2
                edge_widths.append(0.5 + spring_energy / 1000.0)  # 归一化宽度
            
            # 绘制网络图
            nx.draw_networkx(formation_graph, pos=node_pos, 
                             node_color=colors[:num_auvs], 
                             node_size=500, 
                             width=edge_widths,
                             with_labels=True, 
                             font_weight='bold',
                             font_color='white')
            
            plt.title(f'编队拓扑结构 ({formation_topology})', fontsize=14)
            plt.axis('off')
            
            # 保存拓扑结构图
            topology_path = os.path.join(output_dir, 'formation_topology.png')
            plt.savefig(topology_path, dpi=300, bbox_inches='tight')
            print(f"编队拓扑结构图已保存到: {topology_path}")
            
            plt.show()

if __name__ == "__main__":
    import argparse
    
    # 配置命令行参数
    parser = argparse.ArgumentParser(description='多AUV海洋环境仿真')
    parser.add_argument('--steps', type=int, default=800000, help='最大仿真步数')
    parser.add_argument('--continue', dest='continue_sim', action='store_true', help='从上次保存的状态继续仿真')
    parser.add_argument('--no-save', dest='save_results', action='store_false', help='不保存仿真结果')
    parser.add_argument('--no-auto-stop', dest='auto_stop', action='store_false', help='禁用自动停止(即使到达目标也运行所有步数)')
    parser.add_argument('--target-distance', dest='target_distance', type=float, default=0.5, help='到达目标的距离阈值(km)')
    parser.add_argument('--no-pass-through', dest='pass_through', action='store_false', help='禁用穿越模式(到达目标后停止而不是继续前进)')
    parser.add_argument('--no-flexible', dest='use_flexible', action='store_false', help='不使用柔性多体动力学模型')
    parser.add_argument('--stiffness', type=float, default=180.0, help='弹簧刚度')
    parser.add_argument('--damping', type=float, default=150.0, help='阻尼系数')
    parser.add_argument('--topology', type=str, choices=['hierarchical', 'mesh', 'line', 'star'], default='hierarchical', help='编队拓扑结构')
    parser.add_argument('--mass', type=float, default=500.0, help='AUV质量(kg)')
    parser.add_argument('--consensus', type=float, default=0.28, help='速度一致性增益')
    parser.add_argument('--record-interval', type=int, default=10, help='数据记录间隔(步)')
    parser.add_argument('--skip-plots', action='store_true', help='跳过生成图表')
    
    # 设置pass_through的默认值
    parser.set_defaults(pass_through=True)

    args = parser.parse_args()
    
    try:
        main(max_steps=args.steps,
             load_state=args.continue_sim, 
             save_results=args.save_results,
             auto_stop=args.auto_stop,
             target_reach_distance=args.target_distance,
             use_flexible_model=args.use_flexible,
             spring_stiffness=args.stiffness,
             damping_coefficient=args.damping,
             formation_topology=args.topology,
             auv_mass=args.mass,
             consensus_gain=args.consensus,
             data_record_interval=args.record_interval,
             skip_plots=args.skip_plots,
             pass_through=args.pass_through)
    except Exception as e:
        print(f"程序发生异常： {e}")
        traceback.print_exc() 