import numpy as np
import matplotlib
# 使用Agg后端，不需要Tcl/Tk
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from AdjustAngle import AdjustAngle
import xarray as xr
from ocean_field import OceanCurrentField
from infante import Infante_3d_with_current
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
from limitmaxmin import LimitMaxMin
from scipy.interpolate import RegularGridInterpolator
from matplotlib.cm import get_cmap
from pyproj import Geod
from scipy.ndimage import zoom
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# --- 加载海流数据 ---（保持原始顺序）
ds = xr.open_dataset("D:/ocean_data/cmems_mod_glo_phy_my_0.083deg_P1D-m_1751179968305.nc")
uo = ds['uo'].isel(time=0).values  # 形状应为 (depth, lat, lon)
print("提取后 uo shape:", uo.shape)

print(ds['uo'].dims)


vo = ds['vo'].isel(time=0).values
lat = ds['latitude'].values
lon = ds['longitude'].values
depth = ds['depth'].values
print(f"海图纬度范围: {lat.min()} ~ {lat.max()}")
print(f"海图经度范围: {lon.min()} ~ {lon.max()}")
print(f"海图深度范围: {depth.min()} ~ {depth.max()}")

# 不要转置，直接使用原始的 uo, vo 数据
uo_interp = RegularGridInterpolator((depth, lat, lon), uo)
vo_interp = RegularGridInterpolator((depth, lat, lon), vo)

# 构建插值器：注意维度顺序 (depth, lat, lon)
# 调整维度为 (lat, lon, depth)
uo_corrected = uo
vo_corrected = vo
# 构建插值器：注意维度顺序 (depth, lat, lon)
uo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),  # 深度倒序使其从浅到深
    uo_corrected[::-1, :, :]  # 数据也同步倒序
)
vo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),
    vo_corrected[::-1, :, :]
)

print("uo shape:", uo.shape)  # 应该是 (13, x, x)
# 取一个固定位置，检查不同深度上的流速变化
lat_idx = 6  # 中间纬度
lon_idx = 6  # 中间经度

print("\n=== 检查不同深度的流速变化 ===")
for i, d in enumerate(depth):
  print(f"Depth = {d:.1f}m: uo = {uo[i, lat_idx, lon_idx]:.4f}, vo = {vo[i, lat_idx, lon_idx]:.4f}")


print("depth:", depth.shape)  # 应该是 (13,)
print("lat:", lat.shape)
print("lon:", lon.shape)


# 初始化插值器
ocean = OceanCurrentField(uo_corrected, vo_corrected, lat, lon, depth)

# 初始经纬度（例如南海）- 中心位置
lat0 = 19.5  # 改为经纬度范围的中间值 (19°-20°的中间)
lon0 = 115.5  # 改为经纬度范围的中间值 (115°-116°的中间)

# 将初始位置移到图像中心
lat0 = (lat[0] + lat[-1]) / 2  # 使用纬度的中点
lon0 = (lon[0] + lon[-1]) / 2  # 使用经度的中点


# 角度转换常数
R2D = 180 / np.pi
D2R = np.pi / 180


# 辅助函数
def LimitMaxMin(value, max_val, min_val):
    return max(min(value, max_val), min_val)
def xy_to_latlon(x, y, lat0, lon0):
    R = 6371000
    dlat = y / R
    dlon = x / (R * np.cos(np.radians(lat0)))
    return lat0 + np.degrees(dlat), lon0 + np.degrees(dlon)

# 定义多AUV初始化函数 (修改自circle.py)
def init_AUVs_circle(num_auvs, radius_km, lat0, lon0):
    R_earth = 6371.0
    radius_deg = (radius_km / R_earth) * (180 / np.pi)
    states = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)
    for angle in angles:
        dlat = radius_deg * np.sin(angle)
        dlon = radius_deg * np.cos(angle) / np.cos(np.radians(lat0))
        lat_pos = lat0 + dlat
        lon_pos = lon0 + dlon
        psi = angle + np.pi  # 初始航向指向圆心
        
        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        # 计算初始位置的相对坐标
        x_p = (lon_pos - lon0) * 111000 * np.cos(lat0 * np.pi / 180)
        y_p = (lat_pos - lat0) * 111000
        z_p = 0
        
        state = np.array([0, 0, 0, 0, 0, x_p, y_p, z_p, 0, psi])
        states.append(state)
    return np.array(states)


# 主函数
def main():
    # 参数初始化
    h = 0.1  # 时间步长 (秒)
    m = 5000  # 增加时间步数，使轨迹更长
    num_auvs = 10  # AUV数量，改为10个与图片一致
    
    # 创建不同的推力和舵角设置 - 增加推力使AUV能够向北移动
    thrust_settings = np.linspace(300, 350, num_auvs)  # 增大推力范围
    deltah_settings = np.zeros(num_auvs)  # 垂直舵角全部设为0，避免下潜
    deltav_settings = np.linspace(-0.05, 0.05, num_auvs) * D2R  # 添加小的水平舵角差异
    
    # 使用彩虹色谱为每个AUV分配不同颜色
    colors = get_cmap('rainbow')(np.linspace(0, 1, num_auvs))
    
    # 选取表层海流数据
    uo_plot = uo[0]  # 表层海流 u 分量
    vo_plot = vo[0]  # 表层海流 v 分量
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lon, lat)
    
    # 使用pyproj计算相对坐标
    geod = Geod(ellps='WGS84')
    center_lat = (lat[0] + lat[-1]) / 2
    center_lon = (lon[0] + lon[-1]) / 2
    
    # 计算每个格点相对于中心的距离
    dx_km = np.zeros_like(lon_grid)
    dy_km = np.zeros_like(lat_grid)
    for i in range(lat_grid.shape[0]):
        for j in range(lat_grid.shape[1]):
            az12, _, dist = geod.inv(center_lon, center_lat, lon_grid[i, j], lat_grid[i, j])
            dx_km[i, j] = dist * np.sin(np.radians(az12)) / 1000
            dy_km[i, j] = dist * np.cos(np.radians(az12)) / 1000
    
    # 平移坐标原点至图左下角
    x_shift, y_shift = dx_km.min(), dy_km.min()
    dx_km -= x_shift
    dy_km -= y_shift
    
    # 初始化多个AUV (沿圆周排列) - 使用图1中的圆形编队
    radius_km = 5  # 圆半径，单位公里，修改为5km
    
    # 在坐标系中心点附近创建圆形编队
    states = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)
    for angle in angles:
        # 计算圆上的点（使用统一的公里单位）
        center_x = dx_km.max() / 2
        center_y = dy_km.max() / 2
        x_p = center_x + radius_km * np.cos(angle)
        y_p = center_y + radius_km * np.sin(angle)
        psi = angle + np.pi  # 初始航向指向圆心
        
        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        state = np.array([0, 0, 0, 0, 0, x_p, y_p, 0, 0, psi])  # 统一使用公里单位
        states.append(state)
    
    initial_states = np.array(states)
    
    # 修改初始航向为向上（北方向），并设置初始速度
    for i in range(num_auvs):
        initial_states[i, 9] = np.pi/2  # 航向角设为90度（北方向）
        initial_states[i, 0] = 1.0      # 设置初始前向速度为1.0 m/s
        initial_states[i, 5] *= 1000    # 将初始x位置转换为米
        initial_states[i, 6] *= 1000    # 将初始y位置转换为米
    
    # 为每个AUV创建状态数组
    T = np.zeros(m)
    all_Y = np.zeros((num_auvs, m, 10))  # [AUV编号, 时间步, 状态向量]
    
    # 为每个AUV准备控制输入记录
    all_Force = []
    all_deltav = []
    all_deltah = []
    
    # 为每个AUV创建当前状态向量
    current_states = initial_states.copy()
    
    # 创建数组记录每一步的距离
    step_distances = np.zeros((num_auvs, m))
    
    # 主循环 - 时间步
    for i in range(m):
        t = h * (i + 1)
        T[i] = t
        
        # 处理每个AUV
        for j in range(num_auvs):
            x = current_states[j]
            
            # 当前坐标（根据累积位置计算当前经纬度）
            lat_now = lat0 + x[6] / 111000
            lon_now = lon0 + x[5] / (111000 * np.cos(lat0 * np.pi / 180))
            depth_now = -x[7]  # 注意 z 是负的
            
            # 边界检查改进：限制在数据有效范围内，并添加更大边距
            lat_margin = 0.05  # 增加边距
            lon_margin = 0.05
            depth_margin = 5.0
            
            # 检查是否接近边界，并在接近时提前调整位置
            is_inside = (
                depth.min() + depth_margin <= depth_now <= depth.max() - depth_margin and
                lat.min() + lat_margin <= lat_now <= lat.max() - lat_margin and
                lon.min() + lon_margin <= lon_now <= lon.max() - lon_margin
            )
            
            if not is_inside:
                # 限制在数据有效范围内，避免超出边界
                lat_now = np.clip(lat_now, lat.min() + lat_margin, lat.max() - lat_margin)
                lon_now = np.clip(lon_now, lon.min() + lon_margin, lon.max() - lon_margin)
                depth_now = np.clip(depth_now, depth.min() + depth_margin, depth.max() - depth_margin)
                if i % 200 == 0:
                    print(f"注意：AUV {j} 在步骤 {i} 处限制了位置以避免超出数据范围")

            # 获取当前位置插值的海流速度
            try:
                u_c, v_c = ocean.get_current(depth_now, lat_now, lon_now)
                
                # 检查返回的海流值是否有效
                if np.isnan(u_c) or np.isnan(v_c):
                    # 如果是NaN，尝试使用相邻点的值
                    nearby_depths = [depth_now - 1, depth_now + 1]
                    nearby_lats = [lat_now - 0.01, lat_now + 0.01]
                    nearby_lons = [lon_now - 0.01, lon_now + 0.01]
                    
                    # 尝试多个相邻点的组合
                    for d in nearby_depths:
                        for la in nearby_lats:
                            for lo in nearby_lons:
                                try:
                                    temp_u, temp_v = ocean.get_current(d, la, lo)
                                    if not (np.isnan(temp_u) or np.isnan(temp_v)):
                                        u_c, v_c = temp_u, temp_v
                                        break
                                except:
                                    continue
                
                    # 如果所有相邻点都失败，使用零值
                    if np.isnan(u_c) or np.isnan(v_c):
                        u_c, v_c = 0.0, 0.0
                
                u_c = float(u_c)
                v_c = float(v_c)
                w_c = 0.0
            except Exception:
                u_c, v_c, w_c = 0.0, 0.0, 0.0

            current_vector = [u_c, v_c, w_c]  # 三维海流向量
            
            # 控制输入 - 每个AUV使用不同参数
            Fu = thrust_settings[j]  # 恒定推力
            deltav = deltav_settings[j]  # 水平舵角
            deltah = deltah_settings[j]  # 垂直舵角为0，保持水平运动
            
            # 添加编队保持控制逻辑
            # 计算与其他AUV的距离
            for k in range(num_auvs):
                if j != k:
                    dx = current_states[j][5] - current_states[k][5]
                    dy = current_states[j][6] - current_states[k][6]
                    distance = np.sqrt(dx**2 + dy**2)
                    step_distances[j, i] = distance

                    # 添加编队保持力（简单排斥/吸引力）
                    if distance < 500:  # 如果距离小于500米
                        # 添加排斥力
                        force = 0.1 * (500 - distance) / distance
                        current_states[j][0] -= force * dx / 1000  # 调整速度（将米转换为公里）
                    elif distance > 1500:  # 如果距离大于1500米
                        # 添加吸引力
                        force = 0.05 * (distance - 1500) / distance
                        current_states[j][0] += force * dx / 1000  # 调整速度（将米转换为公里）
            
            # 添加更强的正弦波扰动，模拟图中的波浪形轨迹
            if i > 100:  # 更早开始扰动
                # 更随机的频率和相位
                freq = 0.05 + 0.02 * j + 0.01 * np.sin(t/1000)  # 动态变化的频率
                phase = j * 0.5 + 0.1 * np.sin(t/800)  # 动态变化的相位
                # 更大的幅度
                deltav += 0.3 * np.sin(freq * t + phase) * D2R  # 增大幅度到0.3
                
                # 添加随机扰动
                if i % 50 == 0:  # 每50步添加一次随机扰动
                    deltav += 0.1 * (np.random.rand() - 0.5) * D2R  # 随机扰动
            
            # 龙格-库塔积分
            k1 = Infante_3d_with_current(t, x, [Fu, deltav, deltah], current_vector)
            k2 = Infante_3d_with_current(t + h / 2, x + h / 2 * k1, [Fu, deltav, deltah], current_vector)
            k3 = Infante_3d_with_current(t + h / 2, x + h / 2 * k2, [Fu, deltav, deltah], current_vector)
            k4 = Infante_3d_with_current(t + h, x + h * k3, [Fu, deltav, deltah], current_vector)

            x = x + h / 6 * (k1 + 2 * k2 + 2 * k3 + k4)
            
            # 状态限制
            x[5] = LimitMaxMin(x[5], 1e5, -1e5)  # x 方向
            x[6] = LimitMaxMin(x[6], 1e5, -1e5)  # y 方向
            x[7] = LimitMaxMin(x[7], 0, -0.5)  # z 方向限制在水面以下0.5米，保持水平运动

            # 速度限制 - 放宽限制，允许更大变化
            x[0] = LimitMaxMin(x[0], 3.0, 0.5)  # u 速度限制在0.5-3.0之间，放宽下限
            x[1] = LimitMaxMin(x[1], 1.0, -1.0)  # v 速度限制放宽
            x[2] = LimitMaxMin(x[2], 0.2, -0.2)  # w 速度限制放宽
            x[3] = LimitMaxMin(x[3], 0.1, -0.1)  # q 放宽限制
            x[4] = LimitMaxMin(x[4], 0.1, -0.1)  # r 放宽限制
            
            # 添加速度的随机变化
            if i % 100 == 0:  # 每100步更新一次速度
                x[0] += 0.1 * (np.random.rand() - 0.5)  # 纵向速度随机变化
                x[1] += 0.05 * (np.random.rand() - 0.5)  # 横向速度随机变化

            # 放宽姿态角限制
            x[8] = LimitMaxMin(x[8], 10 * D2R, -10 * D2R)  # theta 限制在±10度，放宽限制
            
            # 航向控制 - 非常弱的向北趋势 + 随机变化
            ideal_heading = np.pi/2  # 理想航向为90度（北方向）
            current_heading = x[9]
            # 计算航向差值，注意处理角度的周期性
            heading_diff = AdjustAngle(ideal_heading - current_heading)
            
            # 非常弱的航向修正，仅提供轻微的向北趋势
            correction = 0.005 * heading_diff  # 极小的修正力度
            
            # 添加航向角的随机变化
            if i % 50 == 0:  # 每50步更新一次航向
                random_heading = 0.05 * (np.random.rand() - 0.5) * D2R  # 添加随机扰动
                x[9] = AdjustAngle(current_heading + correction + random_heading)
            
            # 计算当前步的移动距离（米）
            if i > 0:
                prev_x = all_Y[j, i-1, 5]  # 上一步的x位置
                prev_y = all_Y[j, i-1, 6]  # 上一步的y位置
                dx = x[5] - prev_x
                dy = x[6] - prev_y
                step_dist = np.sqrt(dx**2 + dy**2)  # 欧氏距离
                step_distances[j, i] = step_dist
            
            # 更新当前状态
            current_states[j] = x
            # 记录状态
            all_Y[j, i] = x
        
        # 每隔1000步打印一次进度
        if i % 1000 == 0:
            print(f"仿真进度: {i}/{m} 步 ({i/m*100:.1f}%)")
    
    print("仿真结束，开始生成图表...")
    
    # 将AUV轨迹转换为相对坐标（千米）
    all_tracks_x_km = []
    all_tracks_y_km = []
    
    # 直接使用模型坐标，不进行经纬度转换，这样可以更好地显示运动轨迹
    for j in range(num_auvs):
        # 将米转换为千米
        track_x_km = all_Y[j, :, 5] / 1000.0  # x位置，转换为千米
        track_y_km = all_Y[j, :, 6] / 1000.0  # y位置，转换为千米
        
        all_tracks_x_km.append(track_x_km)
        all_tracks_y_km.append(track_y_km)
    
    # 绘制AUV轨迹图
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # 使用实际海流数据绘制背景
    speed = np.clip(np.sqrt(uo_plot**2 + vo_plot**2), 0.06, 0.11)
    
    contour = ax.contourf(
        dx_km, dy_km, speed,
        cmap='RdBu_r',     # 颜色图选择
        levels=50,          # 等高线数
        vmin=0.06,          # 手动设置最小值
        vmax=0.11           # 手动设置最大值
    )
    
    # 添加颜色条
    tick_min = 0.06
    tick_max = 0.11
    tick_num = 11  # 11个刻度
    cbar = fig.colorbar(contour, ax=ax, ticks=np.linspace(tick_min, tick_max, tick_num))
    cbar.set_label('Current Speed [m/s]', fontsize=16)
    cbar.ax.tick_params(labelsize=14)
    
    # 绘制海流箭头（与circle.py相同）
    from scipy.ndimage import zoom
    
    # 放大倍数（越大越密）
    factor = 2
    uo_dense = zoom(uo_plot, factor)
    vo_dense = zoom(vo_plot, factor)
    x_dense = zoom(dx_km, factor)
    y_dense = zoom(dy_km, factor)
    
    # 画更密的箭头
    ax.quiver(x_dense, y_dense, uo_dense, vo_dense, scale=3e0, color='k', width=0.002)
    
    # 绘制AUV初始圆形排列
    initial_x_km = []
    initial_y_km = []
    
    for j in range(num_auvs):
        # 使用第一个点作为初始位置
        initial_x_km.append(all_tracks_x_km[j][0])
        initial_y_km.append(all_tracks_y_km[j][0])
    
    # 绘制初始圆形排列
    initial_x_km_closed = np.append(initial_x_km, initial_x_km[0])
    initial_y_km_closed = np.append(initial_y_km, initial_y_km[0])
    
    ax.plot(initial_x_km_closed, initial_y_km_closed, color='red', linewidth=2, linestyle='-')  # 连线
    ax.plot(initial_x_km, initial_y_km, marker='^', color='red', markersize=10, linestyle='None')  # 三角形标记
    
    # 添加初始编队标签
    ax.plot([], [], color='red', linewidth=2, linestyle='-', label='Initial Formation')
    
    # 绘制AUV轨迹
    for j in range(num_auvs):
        # 每隔10个点取一个点，减少数据量但保持轨迹清晰
        step = 10
        x_km = np.array(all_tracks_x_km[j])[::step]
        y_km = np.array(all_tracks_y_km[j])[::step]
        
        # 绘制轨迹
        ax.plot(x_km, y_km, '-', color=colors[j], linewidth=2.5, label=f'AUV{j+1}')
        
        # 绘制起点和终点
        ax.plot(all_tracks_x_km[j][0], all_tracks_y_km[j][0], 'o', color=colors[j], markersize=8)
        ax.plot(all_tracks_x_km[j][-1], all_tracks_y_km[j][-1], 's', color=colors[j], markersize=8)
        
        # 添加方向箭头 (每100个点添加一个)
        arrow_step = 100
        for i in range(0, len(all_tracks_x_km[j]), arrow_step):
            if i+arrow_step < len(all_tracks_x_km[j]):
                dx = all_tracks_x_km[j][i+arrow_step] - all_tracks_x_km[j][i]
                dy = all_tracks_y_km[j][i+arrow_step] - all_tracks_y_km[j][i]
                # 归一化箭头长度
                arrow_len = np.sqrt(dx**2 + dy**2)
                if arrow_len > 0:
                    dx = dx / arrow_len * 1.0  # 1.0 km长的箭头
                    dy = dy / arrow_len * 1.0
                    ax.arrow(all_tracks_x_km[j][i], all_tracks_y_km[j][i], dx, dy, 
                            head_width=0.3, head_length=0.5, fc=colors[j], ec=colors[j], alpha=0.7)
    
    # 绘制最终编队形状（红色虚线闭合多边形）
    final_x_km = []
    final_y_km = []
    for j in range(num_auvs):
        final_x_km.append(all_tracks_x_km[j][-1])
        final_y_km.append(all_tracks_y_km[j][-1])
    
    # 闭合最终编队形状
    final_x_km_closed = np.append(final_x_km, final_x_km[0])
    final_y_km_closed = np.append(final_y_km, final_y_km[0])
    
    # 绘制最终编队形状（红色虚线）
    ax.plot(final_x_km_closed, final_y_km_closed, 'r--', linewidth=2.5, label='Final Formation')
    
    # 标签 & 样式
    ax.set_xlabel("X [km]", fontsize=15)
    ax.set_ylabel("Y [km]", fontsize=15)
    ax.set_title("AUV Formation with Large Random Waves (Zoomed)", fontsize=18)
    
    # 精确设置坐标轴原点
    ax.spines['left'].set_position(('data', 0))
    ax.spines['bottom'].set_position(('data', 0))
    ax.spines['right'].set_color('none')
    ax.spines['top'].set_color('none')
    
    # 计算圆形编队的中心点和半径
    center_x = dx_km.max() / 2
    center_y = dy_km.max() / 2
    
    # 设置局部放大的显示范围，以圆形编队为中心
    zoom_radius = 10  # 放大区域的半径（km）
    ax.set_xlim(center_x - zoom_radius, center_x + zoom_radius)
    ax.set_ylim(center_y - zoom_radius, center_y + zoom_radius)
    ax.set_aspect('equal')
    
    # 设置刻度位置
    ax.xaxis.set_ticks_position('bottom')
    ax.yaxis.set_ticks_position('left')
    
    # 设置适合局部放大的刻度间隔
    x_min, x_max = ax.get_xlim()
    y_min, y_max = ax.get_ylim()
    ax.set_xticks(np.arange(np.floor(x_min/5)*5, np.ceil(x_max/5)*5, 5))  # 5km间隔
    ax.set_yticks(np.arange(np.floor(y_min/5)*5, np.ceil(y_max/5)*5, 5))  # 5km间隔
    ax.grid(True, alpha=0.3)  # 添加网格线以便更好地观察
    
    # 添加图例，调整位置和大小
    ax.legend(loc='lower left', fontsize=10, framealpha=0.7)
    
    # 紧凑布局，去除白边
    fig.subplots_adjust(left=0.12, right=0.92, top=0.92, bottom=0.12)
    
    # 打印坐标范围
    print("X范围: 0 ~", dx_km.max(), "km")
    print("Y范围: 0 ~", dy_km.max(), "km")
    
    # 保存图片而不是显示
    plt.savefig('AUV轨迹图.png', dpi=300, bbox_inches='tight')
    print("图片已保存为 'AUV轨迹图.png'")
    
    # plt.show()  # 注释掉显示命令

    print("\n===== AUV运行数据统计 =====")
    for j in range(num_auvs):
        # 计算平均速度 (m/s)
        avg_u = np.mean(all_Y[j, :, 0])  # 纵向速度
        avg_v = np.mean(all_Y[j, :, 1])  # 横向速度
        avg_speed = np.sqrt(avg_u**2 + avg_v**2)  # 合速度
        
        # 计算平均航向角 (度)
        avg_psi = np.mean(all_Y[j, :, 9]) * R2D  # 航向角
        
        # 计算总行程 - 直线距离 (km)
        direct_distance = np.sqrt((all_tracks_x_km[j][-1] - all_tracks_x_km[j][0])**2 + 
                                (all_tracks_y_km[j][-1] - all_tracks_y_km[j][0])**2)
        
        # 计算实际累积行程 (km)
        actual_distance = np.sum(step_distances[j]) / 1000.0  # 转换为km
        
        print(f"AUV{j+1}: 平均速度 = {avg_speed:.2f} m/s, 平均航向 = {avg_psi:.1f}°")
        print(f"      直线距离 = {direct_distance:.2f} km, 实际行程 = {actual_distance:.2f} km")
        
    # 保存每步距离数据到CSV文件
    import pandas as pd
    # 创建DataFrame
    columns = [f"AUV{j+1}" for j in range(num_auvs)]
    df = pd.DataFrame(step_distances.T, columns=columns)  # 转置使每列对应一个AUV
    df.insert(0, "时间(秒)", T)  # 添加时间列
    
    # 保存到CSV
    df.to_csv("AUV每步距离.csv", index=False)
    print("\n每步距离数据已保存到 'AUV每步距离.csv'")
    
    # 计算累积距离
    cumulative_distances = np.zeros((num_auvs, m))
    for j in range(num_auvs):
        cumulative_distances[j] = np.cumsum(step_distances[j]) / 1000.0  # 转换为km
    
    # 创建累积距离图
    fig_dist, ax_dist = plt.subplots(figsize=(12, 6))
    
    # 绘制每个AUV的累积距离
    for j in range(num_auvs):
        ax_dist.plot(T, cumulative_distances[j], '-', color=colors[j], linewidth=1.5, label=f'AUV{j+1}')
    
    # 设置图表属性
    ax_dist.set_xlabel('时间 (秒)', fontsize=12)
    ax_dist.set_ylabel('累积距离 (km)', fontsize=12)
    ax_dist.set_title('AUV累积行程随时间变化', fontsize=14)
    ax_dist.grid(True, alpha=0.3)
    ax_dist.legend(loc='upper left', fontsize=10)
    
    # 保存累积距离图
    plt.tight_layout()
    plt.savefig('AUV累积距离图.png', dpi=300, bbox_inches='tight')
    print("累积距离图已保存为 'AUV累积距离图.png'")

    # 创建速度变化图
    fig_speed, ax_speed = plt.subplots(figsize=(12, 6))

    # 绘制每个AUV的速度变化
    for j in range(num_auvs):
        # 计算合速度 (m/s)
        speeds = np.sqrt(all_Y[j, :, 0]**2 + all_Y[j, :, 1]**2)
        
        # 每隔100个点取一个点，减少数据量
        step = 100
        times = np.array(T[::step])
        speeds = np.array(speeds[::step])
        
        # 绘制速度曲线
        ax_speed.plot(times, speeds, '-', color=colors[j], linewidth=1.5, label=f'AUV{j+1}')

    # 设置图表属性
    ax_speed.set_xlabel('时间 (秒)', fontsize=12)
    ax_speed.set_ylabel('速度 (m/s)', fontsize=12)
    ax_speed.set_title('AUV速度随时间变化', fontsize=14)
    ax_speed.grid(True, alpha=0.3)
    ax_speed.legend(loc='upper right', fontsize=10)

    # 保存速度图
    plt.tight_layout()
    plt.savefig('AUV速度图.png', dpi=300, bbox_inches='tight')
    print("速度图已保存为 'AUV速度图.png'")

    # 创建航向角变化图
    fig_heading, ax_heading = plt.subplots(figsize=(12, 6))

    # 绘制每个AUV的航向角变化
    for j in range(num_auvs):
        # 将航向角转换为度
        headings = all_Y[j, :, 9] * R2D
        
        # 每隔100个点取一个点，减少数据量
        step = 100
        times = np.array(T[::step])
        headings = np.array(headings[::step])
        
        # 绘制航向角曲线
        ax_heading.plot(times, headings, '-', color=colors[j], linewidth=1.5, label=f'AUV{j+1}')

    # 设置图表属性
    ax_heading.set_xlabel('时间 (秒)', fontsize=12)
    ax_heading.set_ylabel('航向角 (度)', fontsize=12)
    ax_heading.set_title('AUV航向角随时间变化', fontsize=14)
    ax_heading.grid(True, alpha=0.3)
    ax_heading.legend(loc='upper right', fontsize=10)

    # 保存航向角图
    plt.tight_layout()
    plt.savefig('AUV航向角图.png', dpi=300, bbox_inches='tight')
    print("航向角图已保存为 'AUV航向角图.png'")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print("程序发生异常：", e)
        import traceback
        traceback.print_exc() 