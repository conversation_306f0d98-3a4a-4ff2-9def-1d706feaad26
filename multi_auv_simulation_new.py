import numpy as np
import matplotlib
# 使用默认后端，允许图形窗口弹出
# matplotlib.use('Agg')  # 注释掉此行以允许弹窗显示
import matplotlib.pyplot as plt
from AdjustAngle import AdjustAngle
import xarray as xr
from ocean_field import OceanCurrentField
from infante import Infante_3d_with_current
from mpl_toolkits.mplot3d import Axes3D
import matplotlib
from limitmaxmin import LimitMaxMin
from scipy.interpolate import RegularGridInterpolator
from matplotlib.cm import get_cmap
from pyproj import Geod
from scipy.ndimage import zoom
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号

# --- 加载海流数据 ---（保持原始顺序）
ds = xr.open_dataset("D:/ocean_data/cmems_mod_glo_phy_my_0.083deg_P1D-m_1751179968305.nc")
uo = ds['uo'].isel(time=0).values  # 形状应为 (depth, lat, lon)
print("提取后 uo shape:", uo.shape)

print(ds['uo'].dims)


vo = ds['vo'].isel(time=0).values
lat = ds['latitude'].values
lon = ds['longitude'].values
depth = ds['depth'].values
print(f"海图纬度范围: {lat.min()} ~ {lat.max()}")
print(f"海图经度范围: {lon.min()} ~ {lon.max()}")
print(f"海图深度范围: {depth.min()} ~ {depth.max()}")

# 不要转置，直接使用原始的 uo, vo 数据
uo_interp = RegularGridInterpolator((depth, lat, lon), uo)
vo_interp = RegularGridInterpolator((depth, lat, lon), vo)

# 构建插值器：注意维度顺序 (depth, lat, lon)
# 调整维度为 (lat, lon, depth)
uo_corrected = uo
vo_corrected = vo
# 构建插值器：注意维度顺序 (depth, lat, lon)
uo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),  # 深度倒序使其从浅到深
    uo_corrected[::-1, :, :]  # 数据也同步倒序
)
vo_interp = RegularGridInterpolator(
    (depth[::-1], lat, lon),
    vo_corrected[::-1, :, :]
)

print("uo shape:", uo.shape)  # 应该是 (13, x, x)
# 取一个固定位置，检查不同深度上的流速变化
lat_idx = 6  # 中间纬度
lon_idx = 6  # 中间经度

print("\n=== 检查不同深度的流速变化 ===")
for i, d in enumerate(depth):
  print(f"Depth = {d:.1f}m: uo = {uo[i, lat_idx, lon_idx]:.4f}, vo = {vo[i, lat_idx, lon_idx]:.4f}")


print("depth:", depth.shape)  # 应该是 (13,)
print("lat:", lat.shape)
print("lon:", lon.shape)


# 初始化插值器
ocean = OceanCurrentField(uo_corrected, vo_corrected, lat, lon, depth)

# 初始经纬度（例如南海）- 中心位置
lat0 = 19.5  # 改为经纬度范围的中间值 (19°-20°的中间)
lon0 = 115.5  # 改为经纬度范围的中间值 (115°-116°的中间)

# 将初始位置移到图像中心
lat0 = (lat[0] + lat[-1]) / 2  # 使用纬度的中点
lon0 = (lon[0] + lon[-1]) / 2  # 使用经度的中点


# 角度转换常数
R2D = 180 / np.pi
D2R = np.pi / 180


# 辅助函数
def LimitMaxMin(value, max_val, min_val):
    return max(min(value, max_val), min_val)
def xy_to_latlon(x, y, lat0, lon0):
    R = 6371000
    dlat = y / R
    dlon = x / (R * np.cos(np.radians(lat0)))
    return lat0 + np.degrees(dlat), lon0 + np.degrees(dlon)

# 定义多AUV初始化函数 (修改自circle.py)
def init_AUVs_circle(num_auvs, radius_km, lat0, lon0):
    R_earth = 6371.0
    radius_deg = (radius_km / R_earth) * (180 / np.pi)
    states = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)
    for angle in angles:
        dlat = radius_deg * np.sin(angle)
        dlon = radius_deg * np.cos(angle) / np.cos(np.radians(lat0))
        lat_pos = lat0 + dlat
        lon_pos = lon0 + dlon
        psi = angle + np.pi  # 初始航向指向圆心
        
        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        # 计算初始位置的相对坐标
        x_p = (lon_pos - lon0) * 111000 * np.cos(lat0 * np.pi / 180)
        y_p = (lat_pos - lat0) * 111000
        z_p = 0
        
        state = np.array([0, 0, 0, 0, 0, x_p, y_p, z_p, 0, psi])
        states.append(state)
    return np.array(states)


# 主函数
def main():
    # 参数初始化
    h = 0.1  # 时间步长 (秒)
    m = 12500 # 增加时间步数，使轨迹更长
    num_auvs = 10  # AUV数量，改为10个与图片一致
    
    # 创建不同的推力和舵角设置 - 大幅增加推力以支持20倍速度
    thrust_settings = np.linspace(6000, 7000, num_auvs)  # 推力扩大20倍
    deltah_settings = np.zeros(num_auvs)  # 垂直舵角全部设为0，避免下潜
    deltav_settings = np.linspace(-0.05, 0.05, num_auvs) * D2R  # 添加小的水平舵角差异
    
    # 使用彩虹色谱为每个AUV分配不同颜色
    colors = get_cmap('rainbow')(np.linspace(0, 1, num_auvs))
    
    # 选取表层海流数据
    uo_plot = uo[0]  # 表层海流 u 分量
    vo_plot = vo[0]  # 表层海流 v 分量
    
    # 创建经纬度网格
    lon_grid, lat_grid = np.meshgrid(lon, lat)
    
    # 使用pyproj计算相对坐标
    geod = Geod(ellps='WGS84')
    center_lat = (lat[0] + lat[-1]) / 2
    center_lon = (lon[0] + lon[-1]) / 2
    
    # 计算每个格点相对于中心的距离
    dx_km = np.zeros_like(lon_grid)
    dy_km = np.zeros_like(lat_grid)
    for i in range(lat_grid.shape[0]):
        for j in range(lat_grid.shape[1]):
            az12, _, dist = geod.inv(center_lon, center_lat, lon_grid[i, j], lat_grid[i, j])
            dx_km[i, j] = dist * np.sin(np.radians(az12)) / 1000
            dy_km[i, j] = dist * np.cos(np.radians(az12)) / 1000
    
    # 平移坐标原点至图左下角
    x_shift, y_shift = dx_km.min(), dy_km.min()
    dx_km -= x_shift
    dy_km -= y_shift
    
    # 初始化多个AUV (沿圆周排列) - 使用图1中的圆形编队
    radius_km = 5  # 圆半径，单位公里，修改为5km
    
    # 在坐标系中心点附近创建圆形编队
    states = []
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)
    for angle in angles:
        # 计算圆上的点
        center_x = dx_km.max() / 2
        center_y = dy_km.max() / 2
        x_p = center_x + radius_km * np.cos(angle)
        y_p = center_y + radius_km * np.sin(angle)
        psi = angle + np.pi  # 初始航向指向圆心
        
        # 初始状态: [u, v, w, q, r, x_p, y_p, z_p, theta, psi]
        state = np.array([0, 0, 0, 0, 0, x_p * 1000, y_p * 1000, 0, 0, psi])  # 转换为米
        states.append(state)
    
    initial_states = np.array(states)
    
    # 修改初始航向为向上（北方向），并设置初始速度
    for i in range(num_auvs):
        initial_states[i, 9] = np.pi/2  # 航向角设为90度（北方向）
        initial_states[i, 0] = 20.0      # 设置初始前向速度为20.0 m/s (扩大20倍)
    
    # 为每个AUV创建状态数组
    T = np.zeros(m)
    all_Y = np.zeros((num_auvs, m, 10))  # [AUV编号, 时间步, 状态向量]
    
    # 为每个AUV准备控制输入记录
    all_Force = []
    all_deltav = []
    all_deltah = []
    
    # 为每个AUV创建当前状态向量
    current_states = initial_states.copy()
    
    # 主循环 - 时间步
    for i in range(m):
        t = h * (i + 1)
        T[i] = t
        
        # 处理每个AUV
        for j in range(num_auvs):
            x = current_states[j]
            
            # 当前坐标（根据累积位置计算当前经纬度）
            lat_now = lat0 + x[6] / 111000
            lon_now = lon0 + x[5] / (111000 * np.cos(lat0 * np.pi / 180))
            depth_now = -x[7]  # 注意 z 是负的
            
            # 边界检查改进：限制在数据有效范围内，并添加更大边距
            lat_margin = 0.05  # 增加边距
            lon_margin = 0.05
            depth_margin = 5.0
            
            # 检查是否接近边界，并在接近时提前调整位置
            is_inside = (
                depth.min() + depth_margin <= depth_now <= depth.max() - depth_margin and
                lat.min() + lat_margin <= lat_now <= lat.max() - lat_margin and
                lon.min() + lon_margin <= lon_now <= lon.max() - lon_margin
            )
            
            if not is_inside:
                # 限制在数据有效范围内，避免超出边界
                lat_now = np.clip(lat_now, lat.min() + lat_margin, lat.max() - lat_margin)
                lon_now = np.clip(lon_now, lon.min() + lon_margin, lon.max() - lon_margin)
                depth_now = np.clip(depth_now, depth.min() + depth_margin, depth.max() - depth_margin)
                if i % 200 == 0:
                    print(f"注意：AUV {j} 在步骤 {i} 处限制了位置以避免超出数据范围")

            # 获取当前位置插值的海流速度
            try:
                u_c, v_c = ocean.get_current(depth_now, lat_now, lon_now)
                
                # 检查返回的海流值是否有效
                if np.isnan(u_c) or np.isnan(v_c):
                    # 如果是NaN，尝试使用相邻点的值
                    nearby_depths = [depth_now - 1, depth_now + 1]
                    nearby_lats = [lat_now - 0.01, lat_now + 0.01]
                    nearby_lons = [lon_now - 0.01, lon_now + 0.01]
                    
                    # 尝试多个相邻点的组合
                    for d in nearby_depths:
                        for la in nearby_lats:
                            for lo in nearby_lons:
                                try:
                                    temp_u, temp_v = ocean.get_current(d, la, lo)
                                    if not (np.isnan(temp_u) or np.isnan(temp_v)):
                                        u_c, v_c = temp_u, temp_v
                                        break
                                except:
                                    continue
                
                    # 如果所有相邻点都失败，使用零值
                    if np.isnan(u_c) or np.isnan(v_c):
                        u_c, v_c = 0.0, 0.0
                
                u_c = float(u_c)
                v_c = float(v_c)
                w_c = 0.0
            except Exception:
                u_c, v_c, w_c = 0.0, 0.0, 0.0

            current_vector = [u_c, v_c, w_c]  # 三维海流向量
            
            # 控制输入 - 每个AUV使用不同参数
            Fu = thrust_settings[j]  # 恒定推力
            deltav = deltav_settings[j]  # 水平舵角
            deltah = deltah_settings[j]  # 垂直舵角为0，保持水平运动
            
            # 完全由海流影响的扰动 - 更直接地受海流影响
            if i > 50:  # 更早开始受到海流影响
                # 计算海流强度和方向
                current_strength = np.sqrt(u_c**2 + v_c**2)
                current_direction = np.arctan2(v_c, u_c)
                
                # 计算海流方向与AUV当前航向的夹角
                heading_current_diff = AdjustAngle(current_direction - x[9])
                
                # 海流对舵角的直接影响 - 海流垂直于航向时影响最大
                # 使用正弦函数模拟横向力的影响，更真实
                # 海流强度越大，影响越大；夹角越接近90度，影响越大
                cross_flow_effect = np.sin(heading_current_diff) * current_strength * 10
                
                # 直接将海流的横向力转化为舵角变化，不添加额外随机性
                deltav += cross_flow_effect * D2R
            
            # 龙格-库塔积分
            k1 = Infante_3d_with_current(t, x, [Fu, deltav, deltah], current_vector)
            k2 = Infante_3d_with_current(t + h / 2, x + h / 2 * k1, [Fu, deltav, deltah], current_vector)
            k3 = Infante_3d_with_current(t + h / 2, x + h / 2 * k2, [Fu, deltav, deltah], current_vector)
            k4 = Infante_3d_with_current(t + h, x + h * k3, [Fu, deltav, deltah], current_vector)

            x = x + h / 6 * (k1 + 2 * k2 + 2 * k3 + k4)
            
            # 状态限制
            x[5] = LimitMaxMin(x[5], 1e5, -1e5)  # x 方向
            x[6] = LimitMaxMin(x[6], 1e5, -1e5)  # y 方向
            x[7] = LimitMaxMin(x[7], 0, -0.5)  # z 方向限制在水面以下0.5米，保持水平运动

            # 速度限制 - 放宽限制，速度扩大20倍
            x[0] = LimitMaxMin(x[0], 60.0, 10.0)  # u 速度限制在10.0-60.0之间 (扩大20倍)
            x[1] = LimitMaxMin(x[1], 20.0, -20.0)  # v 速度限制扩大20倍
            x[2] = LimitMaxMin(x[2], 4.0, -4.0)  # w 速度限制扩大20倍
            x[3] = LimitMaxMin(x[3], 0.1, -0.1)  # q 放宽限制
            x[4] = LimitMaxMin(x[4], 0.1, -0.1)  # r 放宽限制
            
            # 速度直接受海流影响 (扩大20倍)
            # 使用流体动力学原理，更真实地模拟海流影响
            
            # 计算相对流速 - AUV速度与海流速度的矢量差
            # 将海流速度转换到AUV坐标系中
            heading_angle = x[9]
            
            # 海流在AUV坐标系中的分量
            u_c_body = u_c * np.cos(heading_angle) + v_c * np.sin(heading_angle)  # 前向分量
            v_c_body = -u_c * np.sin(heading_angle) + v_c * np.cos(heading_angle)  # 横向分量
            
            # 海流的直接影响 - 没有随机性
            # AUV在水流中的相对速度会改变其绝对速度
            # 海流加速度影响与相对速度和入流角有关
            
            # 直接施加海流影响，放大20倍以使效果更明显
            flow_effect_u = u_c_body * 20
            flow_effect_v = v_c_body * 20
            
            # 直接影响AUV速度
            x[0] = 20.0 + flow_effect_u  # 基础速度加上海流影响
            x[1] = flow_effect_v  # 横向速度直接受海流影响

            # 放宽姿态角限制
            x[8] = LimitMaxMin(x[8], 10 * D2R, -10 * D2R)  # theta 限制在±10度，放宽限制
            
            # 航向控制 - 完全由流体动力学效应决定
            current_heading = x[9]
            
            # 计算横向海流力矩 - 真实流体动力学效应
            # 海流对航向的影响主要是通过横向分量产生的力矩
            
            # 计算海流速度在AUV体轴系中的分量
            heading_angle = current_heading
            u_c_body = u_c * np.cos(heading_angle) + v_c * np.sin(heading_angle)
            v_c_body = -u_c * np.sin(heading_angle) + v_c * np.cos(heading_angle)
            
            # 横向海流产生的力矩与横向速度成正比，与海流强度也成正比
            # 这是一个简化的流体动力学模型
            yaw_moment = v_c_body * 5.0  # 横向海流导致的航向力矩
            
            # 航向变化率取决于力矩
            yaw_rate = yaw_moment * 0.02  # 力矩转换为航向变化率
            
            # 直接更新航向 - 无随机成分，完全由海流决定
            x[9] = AdjustAngle(current_heading + yaw_rate)
            
            # 更新当前状态
            current_states[j] = x
            # 记录状态
            all_Y[j, i] = x
        
        # 每隔1000步打印一次进度
        if i % 1000 == 0:
            print(f"仿真进度: {i}/{m} 步 ({i/m*100:.1f}%)")
    
    print("仿真结束，开始生成图表...")
    
    # 将AUV轨迹转换为相对坐标（千米）
    all_tracks_x_km = []
    all_tracks_y_km = []
    
    # 直接使用模型坐标，不进行经纬度转换，这样可以更好地显示运动轨迹
    for j in range(num_auvs):
        # 将米转换为千米
        track_x_km = all_Y[j, :, 5] / 1000.0  # x位置，转换为千米
        track_y_km = all_Y[j, :, 6] / 1000.0  # y位置，转换为千米
        
        all_tracks_x_km.append(track_x_km)
        all_tracks_y_km.append(track_y_km)
    
    # 绘制AUV轨迹图 - 严格匹配circle.py中的格式
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # 使用实际海流数据绘制背景
    speed = np.clip(np.sqrt(uo_plot**2 + vo_plot**2), 0.06, 0.11)
    
    contour = ax.contourf(
        dx_km, dy_km, speed,
        cmap='RdBu_r',     # 颜色图选择
        levels=50,          # 等高线数
        vmin=0.06,          # 手动设置最小值
        vmax=0.11           # 手动设置最大值
    )
    
    # 添加颜色条 - 精确匹配circle.py的设置
    tick_min = 0.06
    tick_max = 0.11
    tick_num = 11  # 11个刻度
    cbar = fig.colorbar(contour, ax=ax, ticks=np.linspace(tick_min, tick_max, tick_num))
    cbar.set_label('Current Speed [m/s]', fontsize=16)
    cbar.ax.tick_params(labelsize=14)
    
    # 绘制海流箭头（与circle.py相同）
    from scipy.ndimage import zoom
    
    # 放大倍数（越大越密）
    factor = 2
    uo_dense = zoom(uo_plot, factor)
    vo_dense = zoom(vo_plot, factor)
    x_dense = zoom(dx_km, factor)
    y_dense = zoom(dy_km, factor)
    
    # 画更密的箭头
    ax.quiver(x_dense, y_dense, uo_dense, vo_dense, scale=3e0, color='k', width=0.002)
    
    # 绘制AUV初始圆形排列
    initial_x_km = []
    initial_y_km = []
    
    for j in range(num_auvs):
        # 使用第一个点作为初始位置
        initial_x_km.append(all_tracks_x_km[j][0])
        initial_y_km.append(all_tracks_y_km[j][0])
    
    # 绘制初始圆形排列
    initial_x_km_closed = np.append(initial_x_km, initial_x_km[0])
    initial_y_km_closed = np.append(initial_y_km, initial_y_km[0])
    
    ax.plot(initial_x_km_closed, initial_y_km_closed, color='red', linewidth=2, linestyle='-')  # 连线
    ax.plot(initial_x_km, initial_y_km, marker='^', color='red', markersize=10, linestyle='None')  # 三角形标记
    
    # 添加初始编队标签
    ax.plot([], [], color='red', linewidth=2, linestyle='-', label='Initial Formation')
    
    # 绘制AUV轨迹
    for j in range(num_auvs):
        # 每隔10个点取一个点，减少数据量但保持轨迹清晰
        step = 10
        x_km = np.array(all_tracks_x_km[j])[::step]
        y_km = np.array(all_tracks_y_km[j])[::step]
        
        # 绘制轨迹
        ax.plot(x_km, y_km, '-', color=colors[j], linewidth=2.5, label=f'AUV{j+1}')
        
        # 绘制起点和终点
        ax.plot(all_tracks_x_km[j][0], all_tracks_y_km[j][0], 'o', color=colors[j], markersize=8)
        ax.plot(all_tracks_x_km[j][-1], all_tracks_y_km[j][-1], 's', color=colors[j], markersize=8)
        
        # 移除航向角箭头，保持图形整洁
    
    # 绘制最终编队形状（红色虚线闭合多边形）
    final_x_km = []
    final_y_km = []
    for j in range(num_auvs):
        final_x_km.append(all_tracks_x_km[j][-1])
        final_y_km.append(all_tracks_y_km[j][-1])
    
    # 闭合最终编队形状
    final_x_km_closed = np.append(final_x_km, final_x_km[0])
    final_y_km_closed = np.append(final_y_km, final_y_km[0])
    
    # 绘制最终编队形状（红色虚线）
    ax.plot(final_x_km_closed, final_y_km_closed, 'r--', linewidth=2.5, label='Final Formation')
    
    # 标签 & 样式 - 精确匹配circle.py
    ax.set_xlabel("X [km]", fontsize=15)
    ax.set_ylabel("Y [km]", fontsize=15)
    ax.set_title("Glider Initial Positions", fontsize=20)
    
    # 精确设置坐标轴原点
    ax.spines['left'].set_position(('data', 0))
    ax.spines['bottom'].set_position(('data', 0))
    ax.spines['right'].set_color('none')
    ax.spines['top'].set_color('none')
    
    # 设置刻度位置
    ax.xaxis.set_ticks_position('bottom')
    ax.yaxis.set_ticks_position('left')
    ax.set_xticks(np.arange(0, dx_km.max(), 20))
    ax.set_yticks(np.arange(0, dy_km.max(), 20))
    
    ax.set_xlim(0, dx_km.max())
    ax.set_ylim(0, dy_km.max())
    ax.set_aspect('equal')
    ax.grid(False)
    
    # 添加图例，调整位置和大小
    ax.legend(loc='lower left', fontsize=10, framealpha=0.7)
    
    # 紧凑布局，去除白边
    fig.subplots_adjust(left=0.12, right=0.92, top=0.92, bottom=0.12)
    
    # 打印坐标范围
    print("X范围: 0 ~", dx_km.max(), "km")
    print("Y范围: 0 ~", dy_km.max(), "km")
    
    # 显示图片窗口而不是保存
    plt.show()
    
    # 如果需要保存，取消注释下面的代码
    # plt.savefig('AUV轨迹图.png', dpi=300, bbox_inches='tight')
    # print("图片已保存为 'AUV轨迹图.png'")

    print("\n===== AUV运行数据统计 =====")
    for j in range(num_auvs):
        # 计算平均速度 (m/s)
        avg_u = np.mean(all_Y[j, :, 0])  # 纵向速度
        avg_v = np.mean(all_Y[j, :, 1])  # 横向速度
        avg_speed = np.sqrt(avg_u**2 + avg_v**2)  # 合速度
        
        # 计算平均航向角 (度)
        avg_psi = np.mean(all_Y[j, :, 9]) * R2D  # 航向角
        
        # 计算总行程 (km)
        total_distance = np.sqrt((all_tracks_x_km[j][-1] - all_tracks_x_km[j][0])**2 + 
                                (all_tracks_y_km[j][-1] - all_tracks_y_km[j][0])**2)
        
        print(f"AUV{j+1}: 平均速度 = {avg_speed:.2f} m/s, 平均航向 = {avg_psi:.1f}°, 总行程 = {total_distance:.2f} km")

    # 创建速度变化图
    fig_speed, ax_speed = plt.subplots(figsize=(12, 6))

    # 绘制每个AUV的速度变化
    for j in range(num_auvs):
        # 计算合速度 (m/s)
        speeds = np.sqrt(all_Y[j, :, 0]**2 + all_Y[j, :, 1]**2)
        
        # 每隔100个点取一个点，减少数据量
        step = 100
        times = np.array(T[::step])
        speeds = np.array(speeds[::step])
        
        # 绘制速度曲线
        ax_speed.plot(times, speeds, '-', color=colors[j], linewidth=1.5, label=f'AUV{j+1}')

    # 设置图表属性
    ax_speed.set_xlabel('时间 (秒)', fontsize=12)
    ax_speed.set_ylabel('速度 (m/s)', fontsize=12)
    ax_speed.set_title('AUV速度随时间变化', fontsize=14)
    ax_speed.grid(True, alpha=0.3)
    ax_speed.legend(loc='upper right', fontsize=10)

    # 显示速度图
    plt.tight_layout()
    plt.show()
    
    # 如果需要保存，取消注释下面的代码
    # plt.savefig('AUV速度图.png', dpi=300, bbox_inches='tight')
    # print("速度图已保存为 'AUV速度图.png'")

    # 创建航向角变化图
    fig_heading, ax_heading = plt.subplots(figsize=(12, 6))

    # 绘制每个AUV的航向角变化
    for j in range(num_auvs):
        # 将航向角转换为度
        headings = all_Y[j, :, 9] * R2D
        
        # 每隔100个点取一个点，减少数据量
        step = 100
        times = np.array(T[::step])
        headings = np.array(headings[::step])
        
        # 绘制航向角曲线
        ax_heading.plot(times, headings, '-', color=colors[j], linewidth=1.5, label=f'AUV{j+1}')

    # 设置图表属性
    ax_heading.set_xlabel('时间 (秒)', fontsize=12)
    ax_heading.set_ylabel('航向角 (度)', fontsize=12)
    ax_heading.set_title('AUV航向角随时间变化', fontsize=14)
    ax_heading.grid(True, alpha=0.3)
    ax_heading.legend(loc='upper right', fontsize=10)

    # 显示航向角图
    plt.tight_layout()
    plt.show()
    
    # 如果需要保存，取消注释下面的代码
    # plt.savefig('AUV航向角图.png', dpi=300, bbox_inches='tight')
    # print("航向角图已保存为 'AUV航向角图.png'")


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print("程序发生异常：", e)
        import traceback
        traceback.print_exc() 