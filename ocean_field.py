from scipy.interpolate import RegularGridInterpolator

class OceanCurrentField:
    def __init__(self, uo, vo, lat, lon, depth):
        # 维度应为 (depth, lat, lon)，所以顺序是 (depth, lat, lon)
        self.uo_interp = RegularGridInterpolator(
            (depth, lat, lon),  # ✅ 正序，不倒过来
            uo,
            bounds_error=False,
            fill_value=0.0
        )
        self.vo_interp = RegularGridInterpolator(
            (depth, lat, lon),
            vo,
            bounds_error=False,
            fill_value=0.0
        )

    def get_current(self, depth_val, lat_val, lon_val):
        point = [depth_val, lat_val, lon_val]
        u = self.uo_interp(point)
        v = self.vo_interp(point)
        return u, v
