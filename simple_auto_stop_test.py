import numpy as np
import matplotlib.pyplot as plt
import time
import os
import datetime

def main(max_steps=200, auto_stop=True, target_reach_distance=1.0):
    """
    极简版自动停止功能测试
    """
    print("=" * 50)
    print("极简自动停止测试")
    print(f"最大步数: {max_steps}, 自动停止: {'启用' if auto_stop else '禁用'}")
    print("=" * 50)
    
    # 初始位置和目标位置
    start_pos = np.array([10.0, 5.0])  # 起点
    target_pos = np.array([15.0, 8.0])  # 终点
    current_pos = start_pos.copy()  # 当前位置
    
    # 记录轨迹
    trajectory = [current_pos.copy()]
    
    # 记录达到目标信息
    reached_target = False
    target_reached_step = 0
    
    # 开始时间
    start_time = time.time()
    
    # 模拟主循环
    for i in range(max_steps):
        # 计算当前朝向目标的方向
        direction = target_pos - current_pos
        distance = np.linalg.norm(direction)
        
        if distance > 0:
            direction = direction / distance  # 单位向量
        
        # 设定速度和随机扰动
        speed = 0.1
        random_direction = np.random.randn(2) * 0.02
        
        # 更新位置
        current_pos = current_pos + speed * direction + random_direction
        
        # 记录轨迹
        trajectory.append(current_pos.copy())
        
        # 检查是否到达目标
        distance_to_target = np.linalg.norm(current_pos - target_pos)
        
        # 每50步输出状态
        if i % 50 == 0 or i == max_steps - 1:
            print(f"步骤 {i}: 位置 ({current_pos[0]:.2f}, {current_pos[1]:.2f}), 距目标: {distance_to_target:.2f}")
        
        # 判断是否达到目标
        if distance_to_target <= target_reach_distance and not reached_target:
            reached_target = True
            target_reached_step = i
            print(f"\n在步骤 {i} 到达目标区域! 距离: {distance_to_target:.2f}")
            
            # 如果启用自动停止，则结束仿真
            if auto_stop:
                print(f"自动停止已启用，仿真将在达到目标后终止。")
                print(f"总仿真步数: {i+1}/{max_steps} ({(i+1)/max_steps*100:.2f}%)")
                break
            else:
                print("已到达目标区域，但仿真继续进行...")
    
    # 计算实际步数
    actual_steps = target_reached_step + 1 if reached_target and auto_stop else max_steps
    
    # 绘制轨迹
    trajectory = np.array(trajectory[:actual_steps])
    plt.figure(figsize=(8, 6))
    plt.plot(trajectory[:, 0], trajectory[:, 1], 'b-', linewidth=2)
    plt.plot(start_pos[0], start_pos[1], 'go', markersize=10, label='起点')
    plt.plot(target_pos[0], target_pos[1], 'r*', markersize=12, label='目标')
    
    # 绘制目标区域圆圈
    circle = plt.Circle((target_pos[0], target_pos[1]), target_reach_distance, color='r', fill=False, linestyle='--')
    plt.gca().add_patch(circle)
    
    # 如果到达目标，标记到达点
    if reached_target:
        plt.plot(trajectory[target_reached_step, 0], trajectory[target_reached_step, 1], 'ms', markersize=10, label='到达点')
    
    plt.grid(True)
    plt.legend()
    plt.title('自动停止功能测试')
    plt.xlabel('X 坐标')
    plt.ylabel('Y 坐标')
    
    # 显示时间信息
    elapsed_time = time.time() - start_time
    print(f"\n仿真结束! 总用时: {elapsed_time:.2f}秒")
    
    # 结果信息
    print("\n===== 测试结果 =====")
    if reached_target:
        print(f"成功到达目标区域，在步骤 {target_reached_step}")
        if auto_stop:
            print(f"自动停止功能正常工作，仿真在步骤 {target_reached_step} 结束")
    else:
        print("未能到达目标区域")
    
    plt.show()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='极简自动停止功能测试')
    parser.add_argument('--steps', type=int, default=200, help='最大仿真步数')
    parser.add_argument('--no-auto-stop', dest='auto_stop', action='store_false', help='禁用自动停止功能')
    parser.set_defaults(auto_stop=True)
    
    args = parser.parse_args()
    
    main(max_steps=args.steps, auto_stop=args.auto_stop) 