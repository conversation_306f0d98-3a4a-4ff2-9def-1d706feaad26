import numpy as np
import matplotlib.pyplot as plt
import time
import os
import datetime
from AdjustAngle import AdjustAngle
from limitmaxmin import LimitMaxMin

def create_output_directory():
    """创建基于日期时间的输出目录"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"test_output_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def main(max_steps=400, auto_stop=True, target_reach_distance=1.0):
    """
    简化版AUV编队仿真，专门用于测试自动停止功能
    
    参数:
    max_steps - 最大仿真步数
    auto_stop - 是否在到达目标后自动停止
    target_reach_distance - 判定达到目标的距离阈值(km)
    """
    print("=" * 50)
    print("AUV编队自动停止功能测试")
    print(f"最大步数: {max_steps}, 自动停止: {'启用' if auto_stop else '禁用'}")
    print("=" * 50)
    
    # 时间步长
    h = 0.1  # 秒
    
    # AUV数量
    num_auvs = 3
    
    # 目标设置 - 设置在较近的位置
    target_x = 25  # km
    target_y = 15  # km
    
    # 初始位置 (围绕中心点形成圆形编队)
    center_x = 20  # km
    center_y = 10  # km
    radius = 2  # km
    
    # 创建AUV状态数组
    T = np.zeros(max_steps)
    all_positions = np.zeros((num_auvs, max_steps, 2))  # [AUV编号, 时间步, [x,y]位置]
    
    # 初始化AUV位置 (圆形编队)
    angles = np.linspace(0, 2 * np.pi, num_auvs, endpoint=False)
    current_positions = []
    
    for j in range(num_auvs):
        x_pos = center_x + radius * np.cos(angles[j])
        y_pos = center_y + radius * np.sin(angles[j])
        current_positions.append([x_pos, y_pos])
        all_positions[j, 0] = [x_pos, y_pos]
    
    # 记录编队中心
    formation_center = np.zeros((max_steps, 2))
    formation_center[0] = [center_x, center_y]
    
    # 记录是否达到目标
    reached_target = False
    target_reached_time = 0
    
    # 开始仿真
    start_time = time.time()
    print("开始仿真...")
    
    # 主循环
    for i in range(1, max_steps):
        t = i * h
        T[i] = t
        
        # 计算当前编队中心
        current_center = np.mean(current_positions, axis=0)
        formation_center[i] = current_center
        
        # 计算到目标的距离
        dist_to_target = np.sqrt((current_center[0] - target_x)**2 + (current_center[1] - target_y)**2)
        
        # 输出当前状态
        if i % 50 == 0:
            print(f"步骤 {i}: 编队中心位置 ({current_center[0]:.1f}, {current_center[1]:.1f}) km, 距目标: {dist_to_target:.2f} km")
        
        # 检查是否达到目标
        if dist_to_target <= target_reach_distance and not reached_target:
            reached_target = True
            target_reached_time = t
            print(f"\n编队中心在时间 {t:.1f} 秒到达目标区域中心! 距离: {dist_to_target:.2f}km")
            
            # 如果启用自动停止，则终止仿真
            if auto_stop:
                print(f"自动停止已启用，仿真将在达到目标后终止。")
                # 记录实际的仿真步数
                actual_steps = i + 1
                print(f"总仿真步数: {actual_steps}/{max_steps} ({actual_steps/max_steps*100:.2f}%)")
                break
            else:
                print("编队已到达目标区域中心，但仿真继续进行...")
        
        # 更新AUV位置
        for j in range(num_auvs):
            # 计算朝向目标的方向
            current_x, current_y = current_positions[j]
            dx = target_x - current_x
            dy = target_y - current_y
            heading = np.arctan2(dy, dx)
            
            # 简化的运动模型
            speed = 1.0  # m/s
            
            # 添加随机扰动
            dx_random = 0.05 * np.random.randn()
            dy_random = 0.05 * np.random.randn()
            
            # 更新位置
            new_x = current_x + (speed * np.cos(heading) + dx_random) * h
            new_y = current_y + (speed * np.sin(heading) + dy_random) * h
            
            # 保存位置
            current_positions[j] = [new_x, new_y]
            all_positions[j, i] = [new_x, new_y]
    
    # 确保我们有一个有效的actual_steps变量
    if 'actual_steps' not in locals():
        actual_steps = max_steps
    
    # 仿真结束
    elapsed_time = time.time() - start_time
    print(f"\n仿真结束! 总用时: {elapsed_time:.1f}秒, 实际步数: {actual_steps}/{max_steps}")
    
    # 截取实际使用的数据
    T = T[:actual_steps]
    all_positions = all_positions[:, :actual_steps, :]
    formation_center = formation_center[:actual_steps]
    
    # 创建输出目录
    output_dir = create_output_directory()
    print(f"所有输出将保存到: {output_dir}")
    
    # 绘制轨迹
    plt.figure(figsize=(10, 8))
    
    # 绘制AUV轨迹
    colors = ['r', 'g', 'b', 'c', 'm', 'y']
    for j in range(num_auvs):
        plt.plot(all_positions[j, :, 0], all_positions[j, :, 1], '-', color=colors[j % len(colors)], linewidth=1.5, label=f'AUV{j+1}')
        plt.plot(all_positions[j, 0, 0], all_positions[j, 0, 1], 'o', color=colors[j % len(colors)], markersize=8)  # 起点
        plt.plot(all_positions[j, -1, 0], all_positions[j, -1, 1], 's', color=colors[j % len(colors)], markersize=8)  # 终点
    
    # 绘制编队中心轨迹
    plt.plot(formation_center[:, 0], formation_center[:, 1], 'k--', linewidth=2, label='编队中心')
    
    # 绘制目标区域
    circle = plt.Circle((target_x, target_y), target_reach_distance, color='g', fill=False, linestyle='--', linewidth=2)
    plt.gca().add_patch(circle)
    plt.plot(target_x, target_y, 'g*', markersize=15)
    plt.text(target_x + 0.5, target_y + 0.5, '目标区域', fontsize=12, color='g')
    
    # 设置图表属性
    plt.xlabel('X (km)', fontsize=12)
    plt.ylabel('Y (km)', fontsize=12)
    plt.title('AUV编队轨迹和自动停止测试', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(loc='best')
    
    # 保存图表
    plot_path = os.path.join(output_dir, 'trajectory_test.png')
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"轨迹图已保存到: {plot_path}")
    
    # 如果到达目标，显示到达时间
    if reached_target:
        print(f"编队成功到达目标区域，用时 {target_reached_time:.1f} 秒")
        
        # 保存到达目标的数据
        with open(os.path.join(output_dir, 'test_summary.txt'), 'w') as f:
            f.write(f"测试参数:\n")
            f.write(f"最大步数: {max_steps}\n")
            f.write(f"自动停止: {'启用' if auto_stop else '禁用'}\n")
            f.write(f"目标达到判定距离: {target_reach_distance} km\n\n")
            f.write(f"测试结果:\n")
            f.write(f"成功到达目标区域\n")
            f.write(f"到达时间: {target_reached_time:.1f} 秒\n")
            f.write(f"实际仿真步数: {actual_steps}/{max_steps} ({actual_steps/max_steps*100:.2f}%)\n")
            f.write(f"仿真总用时: {elapsed_time:.1f} 秒\n")
        print(f"测试摘要已保存到: {os.path.join(output_dir, 'test_summary.txt')}")
    else:
        print("仿真结束，编队未能到达目标区域")
    
    plt.show()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='AUV编队自动停止功能测试')
    parser.add_argument('--steps', type=int, default=400, help='最大仿真步数')
    parser.add_argument('--no-auto-stop', dest='auto_stop', action='store_false', help='禁用自动停止功能')
    parser.add_argument('--distance', type=float, default=1.0, help='目标达到判定距离(km)')
    parser.set_defaults(auto_stop=True)
    
    args = parser.parse_args()
    
    main(max_steps=args.steps, auto_stop=args.auto_stop, target_reach_distance=args.distance) 