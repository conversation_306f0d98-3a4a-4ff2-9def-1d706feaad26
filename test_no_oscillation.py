#!/usr/bin/env python3
"""
测试无波动AUV编队控制系统
验证AUV到达目标区域后不会产生波动
"""

import subprocess
import sys
import time

def run_simulation(test_name, args):
    """运行仿真测试"""
    print(f"\n{'='*60}")
    print(f"测试: {test_name}")
    print(f"参数: {' '.join(args)}")
    print(f"{'='*60}")
    
    cmd = [sys.executable, "multi_auv_ocean_flow.py"] + args
    
    try:
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5分钟超时
        end_time = time.time()
        
        print(f"运行时间: {end_time - start_time:.1f} 秒")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 测试成功完成")
            # 提取关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if "所有" in line and "AUV" in line and "到达" in line:
                    print(f"📍 {line}")
                elif "穿越模式" in line or "停止模式" in line:
                    print(f"🎯 {line}")
                elif "总仿真步数" in line:
                    print(f"📊 {line}")
        else:
            print("❌ 测试失败")
            print("错误输出:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始无波动AUV编队控制系统测试")
    
    # 测试1: 穿越模式 - 快速测试
    run_simulation(
        "穿越模式测试 (快速)",
        [
            "--steps", "100000",  # 减少步数进行快速测试
            "--target-distance", "0.5",  # 0.5km到达阈值
            "--pass-through",  # 启用穿越模式
            "--auto-stop",  # 启用自动停止
            "--skip-plots"  # 跳过绘图加速测试
        ]
    )
    
    # 测试2: 停止模式 - 快速测试
    run_simulation(
        "停止模式测试 (快速)",
        [
            "--steps", "100000",
            "--target-distance", "0.5",
            "--no-pass-through",  # 禁用穿越模式
            "--auto-stop",
            "--skip-plots"
        ]
    )
    
    # 测试3: 长时间穿越测试
    run_simulation(
        "长时间穿越测试",
        [
            "--steps", "200000",  # 更多步数测试穿越
            "--target-distance", "0.3",  # 更严格的到达阈值
            "--pass-through",
            "--no-auto-stop",  # 不自动停止，测试穿越行为
            "--skip-plots"
        ]
    )
    
    print(f"\n{'='*60}")
    print("🎉 所有测试完成!")
    print("请检查上述结果，确认:")
    print("1. AUV能够成功到达各自的目标点")
    print("2. 到达目标后没有波动或打转现象")
    print("3. 穿越模式下AUV能继续前进")
    print("4. 停止模式下AUV在目标点停止")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
